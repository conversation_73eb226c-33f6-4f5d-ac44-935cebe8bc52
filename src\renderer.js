const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const { scanRedis, batchScanRedis, uploadResults, uploadSingleResult, fetchFeatures, uploadFeature, updateFeature, deleteRemoteFeature, fetchScanResults, fetchScanStats } = require('./redis-scanner');
const { getScanResults, getUnuploadedResults, markResultsAsUploaded, updateRansomwareStatus, saveRansomwareFeature, updateRansomwareFeature, getRansomwareFeatures, deleteRansomwareFeature, getConfig, updateConfig, clearScanResults, saveScanResult, saveBatchScanResults, deduplicateScanResults, syncFeaturesFromRemote, syncScanResultsFromRemote, markFeatureAsSynced, getUnsyncedFeatures, getMappingResults, clearMappingResults, markMappingResultAsTested, markMappingResultsAsTested, getUntestedMappingResults, getMappingStats } = require('./database');
const { exportToExcel } = require('./exporter');
const { MAPPING_MODES, getUserInfo, executeMappingTask, executeAutoMapping, validateTimeRange, formatTimeForQuake } = require('./quake-mapper');

// DOM元素
const tabBtns = document.querySelectorAll('.tab-btn');
const tabPanes = document.querySelectorAll('.tab-pane');
const scanTypeSelect = document.getElementById('scan-type');
const singleInput = document.getElementById('single-input');
const batchInput = document.getElementById('batch-input');
const redisHost = document.getElementById('redis-host');
const redisPort = document.getElementById('redis-port');
const redisTargets = document.getElementById('redis-targets');
const importFileBtn = document.getElementById('import-file');
const fileName = document.getElementById('file-name');
const startScanBtn = document.getElementById('start-scan');
const resultFilter = document.getElementById('result-filter');
const exportResultsBtn = document.getElementById('export-results');
const clearResultsBtn = document.getElementById('clear-results');
const resultsBody = document.getElementById('results-body');
const prevPageBtn = document.getElementById('prev-page');
const nextPageBtn = document.getElementById('next-page');
const pageInfo = document.getElementById('page-info');
const apiServer = document.getElementById('api-server');
const apiKey = document.getElementById('api-key');
const clientId = document.getElementById('client-id');
const clientName = document.getElementById('client-name');
const syncFeaturesBtn = document.getElementById('sync-features');
const featureList = document.getElementById('feature-list');
const newFeature = document.getElementById('new-feature');
const addFeatureBtn = document.getElementById('add-feature');
const saveSettingsBtn = document.getElementById('save-settings');
const detailsModal = document.getElementById('details-modal');
const closeModal = document.querySelector('.close-modal');
const detailHost = document.getElementById('detail-host');
const detailPort = document.getElementById('detail-port');
const detailStatus = document.getElementById('detail-status');
const detailTime = document.getElementById('detail-time');
const detailInfo = document.getElementById('detail-info');
const detailRansomware = document.getElementById('detail-ransomware');
const markRansomwareBtn = document.getElementById('mark-ransomware');
const unmarkRansomwareBtn = document.getElementById('unmark-ransomware');
const keyValueData = document.getElementById('key-value-data');
const matchType = document.getElementById('match-type');

// 通用对话框元素
const commonDialog = document.getElementById('common-dialog');
const dialogTitle = document.getElementById('dialog-title');
const dialogContent = document.getElementById('dialog-content');
const dialogClose = document.getElementById('dialog-close');
const dialogProgress = document.getElementById('dialog-progress');
const dialogProgressFill = document.getElementById('dialog-progress-fill');
const dialogProgressText = document.getElementById('dialog-progress-text');
const dialogResult = document.getElementById('dialog-result');
const dialogButtons = document.getElementById('dialog-buttons');
const resultTotal = document.getElementById('result-total');
const resultSuccess = document.getElementById('result-success');
const resultFail = document.getElementById('result-fail');
const resultRansomware = document.getElementById('result-ransomware');

// 全局变量
let currentPage = 1;
let totalPages = 1;
let pageSize = 10;
let currentResults = [];
let currentDetailId = null;

// 在设置部分添加自动上传开关
const autoUploadSwitch = document.getElementById('auto-upload');
const uploadResultsBtn = document.getElementById('upload-results');

// 多线程扫描相关元素
const useMultiThreadSwitch = document.getElementById('use-multi-thread');
const threadCountInput = document.getElementById('thread-count');

// 测绘相关元素
const quakeTokenInput = document.getElementById('quake-token');
const quakeTokenSettingInput = document.getElementById('quake-token-setting');
const mappingModeSelect = document.getElementById('mapping-mode');
const maxResultsInput = document.getElementById('max-results');
const batchSizeInput = document.getElementById('batch-size');
const startTimeInput = document.getElementById('start-time');
const endTimeInput = document.getElementById('end-time');
const checkTokenBtn = document.getElementById('check-token-btn');
const startMappingBtn = document.getElementById('start-mapping-btn');
const autoMappingBtn = document.getElementById('auto-mapping-btn');
const stopMappingBtn = document.getElementById('stop-mapping-btn');
const clearMappingBtn = document.getElementById('clear-mapping-btn');
const exportTargetsBtn = document.getElementById('export-targets-btn');
const exportUntestedBtn = document.getElementById('export-untested-btn');
const sendUntestedBtn = document.getElementById('send-untested-btn');
const userInfoSpan = document.getElementById('user-info');
const creditInfoSpan = document.getElementById('credit-info');
const persistentCreditInfoSpan = document.getElementById('persistent-credit-info');
const mappingProgressContainer = document.querySelector('.progress-container');
const mappingProgressFill = document.getElementById('mapping-progress-fill');
const mappingProgressText = document.getElementById('mapping-progress-text');
const mappingResultsTable = document.getElementById('mapping-results-table');
const mappingResultsTbody = document.getElementById('mapping-results-tbody');
const mappingTotalCount = document.getElementById('mapping-total-count');
const mappingSuccessCount = document.getElementById('mapping-success-count');
const mappingTestedCount = document.getElementById('mapping-tested-count');
const mappingUntestedCount = document.getElementById('mapping-untested-count');
const mappingLatestTime = document.getElementById('mapping-latest-time');

// 标签页切换
tabBtns.forEach(btn => {
  btn.addEventListener('click', () => {
    const tabId = btn.dataset.tab;

    tabBtns.forEach(b => b.classList.remove('active'));
    tabPanes.forEach(p => p.classList.remove('active'));

    btn.classList.add('active');
    document.getElementById(tabId).classList.add('active');

    // 如果切换到结果页面，加载结果
    if (tabId === 'results') {
      loadResults();
    } else if (tabId === 'settings') {
      loadSettings();
    }
  });
});

// 扫描类型切换
scanTypeSelect.addEventListener('change', () => {
  const scanType = scanTypeSelect.value;

  if (scanType === 'single') {
    singleInput.style.display = 'block';
    batchInput.style.display = 'none';
  } else {
    singleInput.style.display = 'none';
    batchInput.style.display = 'block';
  }
});

// 导入文件
importFileBtn.addEventListener('click', async () => {
  const content = await ipcRenderer.invoke('select-file');

  if (content) {
    redisTargets.value = content;
    fileName.textContent = '已导入文件';
  }
});

// 通用对话框函数
function showDialog(options = {}) {
  const {
    title = '提示',
    content = '',
    showProgress = false,
    showResult = false,
    buttons = [],
    closable = true,
  } = options;

  return new Promise((resolve) => {
    // 设置标题和内容
    dialogTitle.textContent = title;
    dialogContent.innerHTML = content;

    // 显示/隐藏进度条
    dialogProgress.style.display = showProgress ? 'block' : 'none';
    if (showProgress) {
      dialogProgressFill.style.width = '0%';
      dialogProgressText.textContent = '准备中...';
    }

    // 显示/隐藏结果区域
    dialogResult.style.display = showResult ? 'block' : 'none';

    // 设置按钮
    dialogButtons.innerHTML = '';
    buttons.forEach(btn => {
      const button = document.createElement('button');
      button.textContent = btn.text;
      button.className = btn.className || 'primary-btn';
      button.addEventListener('click', () => {
        if (btn.onClick) btn.onClick();
        if (btn.closeDialog) {
          closeDialog();
          // 如果按钮有returnValue，则返回该值，否则返回false
          resolve(btn.returnValue !== undefined ? btn.returnValue : false);
        }
      });
      dialogButtons.appendChild(button);
    });

    // 是否可关闭
    dialogClose.style.display = closable ? 'block' : 'none';

    // 显示对话框
    commonDialog.style.display = 'block';

    // 如果没有按钮，直接resolve false
    if (buttons.length === 0) {
      resolve(false);
    }
  });
}

function closeDialog() {
  commonDialog.style.display = 'none';
}

// 更新进度条
function updateDialogProgress(completed, total) {
  const percent = (completed / total) * 100;
  dialogProgressFill.style.width = `${percent}%`;
  dialogProgressText.textContent = `扫描中... ${completed}/${total}`;
}

// 设置扫描结果
function setDialogResult(total, success, fail, ransomware) {
  resultTotal.textContent = total;
  resultSuccess.textContent = success;
  resultFail.textContent = fail;
  resultRansomware.textContent = ransomware;
}

// 关闭对话框按钮事件
dialogClose.addEventListener('click', closeDialog);

// 点击对话框外部关闭
window.addEventListener('click', (event) => {
  if (event.target === commonDialog) {
    closeDialog();
  }
});

// 开始扫描
startScanBtn.addEventListener('click', async () => {
  const scanType = scanTypeSelect.value;
  let targets = [];

  if (scanType === 'single') {
    const host = redisHost.value.trim();
    const port = redisPort.value.trim();

    if (!host) {
      showDialog({
        title: '提示',
        content: '请输入Redis主机地址',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    targets.push(`${host}:${port || '6379'}`);
  } else {
    const targetsText = redisTargets.value.trim();

    if (!targetsText) {
      showDialog({
        title: '提示',
        content: '请输入批量目标或导入文件',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    targets = targetsText.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));

    if (targets.length === 0) {
      showDialog({
        title: '提示',
        content: '没有有效的目标',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }
  }

  // 显示进度对话框
  showDialog({
    title: '扫描进度',
    content: `即将扫描 ${targets.length} 个目标`,
    showProgress: true,
    closable: false,
    buttons: [{
      text: '取消',
      className: 'danger-btn',
      onClick: () => {
        // 此处可以添加取消扫描的逻辑
        closeDialog();
      }
    }]
  });

  startScanBtn.disabled = true;

  try {
    // 批量扫描
    const totalTargets = targets.length;
    let completedTargets = 0;
    let successCount = 0;
    let failCount = 0;
    let ransomwareCount = 0;

    // 更新进度
    updateDialogProgress(completedTargets, totalTargets);

    // 创建自定义处理函数来跟踪进度
    const onScanComplete = async (result) => {
      completedTargets++;

      if (result.status === 1) {
        successCount++;
        if (result.is_ransomware === 1) {
          ransomwareCount++;
        }
      } else {
        failCount++;
      }

      // 实时保存扫描结果到数据库
      try {
        await saveScanResult(result);
        console.log(`已保存扫描结果: ${result.host}:${result.port}`);
      } catch (error) {
        console.error(`保存扫描结果失败 ${result.host}:${result.port}:`, error.message);
      }

      // 实时标记测绘结果为已测试
      try {
        const target = `${result.host}:${result.port}`;
        await markMappingResultAsTested(result.host, result.port);
        console.log(`已标记测绘目标为已测试: ${target}`);
      } catch (error) {
        console.error(`标记测绘目标为已测试失败 ${result.host}:${result.port}:`, error.message);
      }

      updateDialogProgress(completedTargets, totalTargets);

      // 更新结果统计
      setDialogResult(totalTargets, successCount, failCount, ransomwareCount);

      // 显示结果区域
      dialogResult.style.display = 'block';

      // 如果全部完成，更新按钮
      if (completedTargets === totalTargets) {
        dialogButtons.innerHTML = '';
        const viewResultsBtn = document.createElement('button');
        viewResultsBtn.textContent = '查看结果';
        viewResultsBtn.className = 'primary-btn';
        viewResultsBtn.addEventListener('click', () => {
          closeDialog();
          // 切换到结果页面
          tabBtns.forEach(btn => {
            if (btn.dataset.tab === 'results') {
              btn.click();
            }
          });
        });
        dialogButtons.appendChild(viewResultsBtn);

        // 刷新测绘结果显示
        try {
          if (typeof loadMappingResults === 'function') {
            loadMappingResults();
          }
        } catch (error) {
          console.error('刷新测绘结果显示失败:', error.message);
        }
      }
    };

    // 检查是否使用多线程扫描
    const useMultiThread = await getConfig('use_multi_thread');
    const threadCount = parseInt(await getConfig('thread_count')) || Math.min(require('os').cpus().length, 8);

    let results;
    if (useMultiThread === '1' && targets.length > 1) {
      // 使用多线程扫描（通过IPC调用主进程）
      console.log(`使用多线程扫描，线程数: ${threadCount}`);
      dialogContent.innerHTML = `即将使用 ${threadCount} 个线程扫描 ${targets.length} 个目标`;

      try {
        // 设置IPC事件监听器
        const progressHandler = (event, data) => {
          const { completed, total } = data;
          onProgress(completed, total);
        };

        const completeHandler = (event, result) => {
          onScanComplete(result);
        };

        // 添加事件监听器
        ipcRenderer.on('scan-progress', progressHandler);
        ipcRenderer.on('scan-complete', completeHandler);

        // 调用主进程的多线程扫描
        const response = await ipcRenderer.invoke('batch-scan-multithread', targets, {
          maxThreads: threadCount
        });

        // 移除事件监听器
        ipcRenderer.removeListener('scan-progress', progressHandler);
        ipcRenderer.removeListener('scan-complete', completeHandler);

        if (response.success) {
          results = response.results;
        } else {
          throw new Error(response.error);
        }

      } catch (error) {
        console.error('多线程扫描失败，回退到传统扫描:', error.message);
        dialogContent.innerHTML = `多线程扫描失败，使用传统扫描: ${error.message}`;
        results = await batchScanRedisWithProgress(targets, onScanComplete);
      }
    } else {
      // 使用传统的异步并发扫描
      console.log('使用传统异步并发扫描');
      results = await batchScanRedisWithProgress(targets, onScanComplete);
    }

    // 尝试自动上传结果到远程服务器
    autoUploadScanResults(results);

    // 注意：扫描结果保存和测绘状态标记现在是实时进行的，不需要在这里批量处理
    console.log(`扫描完成，共处理 ${targets.length} 个目标，实时保存了 ${results.length} 个结果`);

    // 执行最终的数据去重操作（确保数据一致性）
    try {
      await deduplicateScanResults();
      console.log('扫描结果去重完成');
    } catch (error) {
      console.error('扫描结果去重失败:', error.message);
    }

  } catch (err) {
    console.error('扫描出错:', err.message);

    showDialog({
      title: '错误',
      content: `扫描出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } finally {
    startScanBtn.disabled = false;
  }
});

// 带进度回调的批量扫描函数
async function batchScanRedisWithProgress(targets, onScanComplete) {
  const results = [];
  const concurrency = 10; // 并发数

  // 创建一个映射，用于跟踪每个host:port的最新扫描结果
  const latestResults = new Map();

  // 将targets解析为host和port
  const parsedTargets = targets.map(target => {
    const [host, port] = target.split(':');
    return { host, port: parseInt(port) || 6379 }; // 默认端口6379
  });

  // 分批处理
  for (let i = 0; i < parsedTargets.length; i += concurrency) {
    const batch = parsedTargets.slice(i, i + concurrency);
    const scanPromises = batch.map(({ host, port }) => {
      return scanRedis(host, port).then(result => {
        // 在本地进行去重，使用host:port作为键
        const key = `${result.host}:${result.port}`;
        latestResults.set(key, result);

        if (onScanComplete) onScanComplete(result);
        return result;
      });
    });

    try {
      const batchResults = await Promise.all(scanPromises);
      results.push(...batchResults);
    } catch (err) {
      console.error('批量扫描出错:', err.message);
      // 继续处理其他批次
    }
  }

  // 使用去重后的结果
  const uniqueResults = Array.from(latestResults.values());

  // 注意：扫描结果现在是通过onScanComplete回调实时保存的，不需要在这里批量保存
  console.log('传统扫描结果已通过实时回调保存到数据库');

  return uniqueResults;
}

// 加载扫描结果
async function loadResults() {
  try {
    // 显示加载中状态
    resultsBody.innerHTML = '<tr><td colspan="10" class="loading-message">正在加载数据，请稍候...</td></tr>';

    const filter = resultFilter.value;
    let status;

    switch (filter) {
      case 'success':
        status = 1;
        break;
      case 'fail':
        status = 0;
        break;
      case 'synced':
        status = 2;
        break;
      case 'unsynced':
        status = 3;
        break;
      default:
        status = undefined;
    }

    // 获取结果
    let results = await getScanResults(status);
    console.log(`获取到 ${results.length} 条扫描结果`);

    // 如果筛选勒索
    if (filter === 'ransomware') {
      results = results.filter(item => item.is_ransomware === 1);
    }

    // 如果筛选已同步
    if (filter === 'synced') {
      results = results.filter(item => item.is_synced === 1);
    }

    // 如果筛选未同步
    if (filter === 'unsynced') {
      results = results.filter(item => item.is_synced === 0);
    }

    // 更新结果计数
    const resultsCount = document.getElementById('results-count');
    if (resultsCount) {
      resultsCount.textContent = `共 ${results.length} 条结果`;
    } else {
      // 如果不存在，创建一个
      const countElement = document.createElement('div');
      countElement.id = 'results-count';
      countElement.className = 'results-count';
      countElement.textContent = `共 ${results.length} 条结果`;

      // 插入到结果表格上方
      const resultsHeader = document.querySelector('.results-header');
      resultsHeader.appendChild(countElement);
    }

    currentResults = results;
    totalPages = Math.ceil(results.length / pageSize);

    if (currentPage > totalPages) {
      currentPage = totalPages || 1;
    }

    updatePagination();
    renderResults();
  } catch (err) {
    console.error('加载结果出错:', err.message);
    resultsBody.innerHTML = `<tr><td colspan="10" class="error-message">加载结果出错: ${err.message}</td></tr>`;
  }
}

// 渲染结果表格
function renderResults() {
  resultsBody.innerHTML = '';

  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const pageResults = currentResults.slice(start, end);

  if (pageResults.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = `<td colspan="9" style="text-align: center;">暂无数据</td>`;
    resultsBody.appendChild(row);
    return;
  }

  pageResults.forEach(item => {
    const row = document.createElement('tr');
    row.className = item.is_ransomware === 1 ? 'ransomware' : (item.status === 1 ? 'success' : 'fail');

    // 确定上传状态
    const uploadStatus = item.is_uploaded ?
      `<span class="upload-status uploaded" title="上传时间: ${formatDate(item.uploaded_at)}">已上传</span>` :
      `<span class="upload-status not-uploaded">未上传</span>`;

    // 确定同步状态
    const syncStatus = item.is_synced ?
      `<span class="sync-status synced" title="远程ID: ${item.remote_id || '未知'}">已同步</span>` :
      `<span class="sync-status not-synced">未同步</span>`;

    // 添加复选框和上传状态
    row.innerHTML = `
      <td><input type="checkbox" class="select-result" data-id="${item.id}" ${item.is_uploaded ? 'disabled' : ''}></td>
      <td>${item.id}</td>
      <td>${item.host}</td>
      <td>${item.port}</td>
      <td>${item.status === 1 ? '成功' : '失败'}</td>
      <td>${formatDate(item.connect_time)}</td>
      <td>${item.status_info}</td>
      <td>${uploadStatus}</td>
      <td>${syncStatus}</td>
      <td>
        <button class="small-btn view-details" data-id="${item.id}">查看详情</button>
        ${!item.is_uploaded ? `<button class="small-btn upload-single" data-id="${item.id}">上传</button>` : ''}
      </td>
    `;

    resultsBody.appendChild(row);
  });

  // 绑定查看详情事件
  document.querySelectorAll('.view-details').forEach(btn => {
    btn.addEventListener('click', () => {
      const id = parseInt(btn.dataset.id);
      showDetails(id);
    });
  });

  // 绑定单个上传事件
  document.querySelectorAll('.upload-single').forEach(btn => {
    btn.addEventListener('click', async () => {
      const id = parseInt(btn.dataset.id);
      await uploadSingleScanResult(id);
      loadResults(); // 重新加载以更新状态
    });
  });

  // 更新全选复选框状态
  const selectAllCheckbox = document.getElementById('select-all-results');
  if (selectAllCheckbox) {
    const checkboxes = document.querySelectorAll('.select-result:not([disabled])');
    selectAllCheckbox.checked = checkboxes.length > 0 &&
                               Array.from(checkboxes).every(cb => cb.checked);
    selectAllCheckbox.indeterminate = !selectAllCheckbox.checked &&
                                     Array.from(checkboxes).some(cb => cb.checked);
  }
}

// 更新分页
function updatePagination() {
  pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
  prevPageBtn.disabled = currentPage <= 1;
  nextPageBtn.disabled = currentPage >= totalPages;
}

// 上一页
prevPageBtn.addEventListener('click', () => {
  if (currentPage > 1) {
    currentPage--;
    renderResults();
    updatePagination();
  }
});

// 下一页
nextPageBtn.addEventListener('click', () => {
  if (currentPage < totalPages) {
    currentPage++;
    renderResults();
    updatePagination();
  }
});

// 筛选结果
resultFilter.addEventListener('change', () => {
  currentPage = 1;
  loadResults();
});

// 导出结果
exportResultsBtn.addEventListener('click', async () => {
  try {
    const filter = resultFilter.value;
    const filePath = await ipcRenderer.invoke('export-data', currentResults, filter);

    if (filePath) {
      const result = await exportToExcel(currentResults, filePath);

      if (result.success) {
        showDialog({
          title: '成功',
          content: '导出成功',
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
      } else {
        showDialog({
          title: '失败',
          content: `导出失败: ${result.message}`,
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
      }
    }
  } catch (err) {
    console.error('导出出错:', err.message);
    showDialog({
      title: '错误',
      content: `导出出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
});

// 清空结果
clearResultsBtn.addEventListener('click', async () => {
  const result = await showDialog({
    title: '确认',
    content: '确定要清空所有结果吗？此操作不可恢复！',
    buttons: [
      {
        text: '取消',
        className: 'secondary-btn',
        closeDialog: true
      },
      {
        text: '确定',
        className: 'danger-btn',
        closeDialog: true,
        returnValue: true
      }
    ]
  });

  if (result) {
    try {
      await clearScanResults();
      await showDialog({
        title: '成功',
        content: '已清空所有扫描结果',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      loadResults(); // 重新加载结果（现在应该为空）
    } catch (err) {
      console.error('清空结果失败:', err.message);
      await showDialog({
        title: '错误',
        content: `清空结果失败: ${err.message}`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  }
});

// 加载设置
async function loadSettings() {
  try {
    // 加载API服务器配置
    const apiUrl = await getConfig('api_server');
    apiServer.value = apiUrl || '';

    // 加载API密钥
    const key = await getConfig('api_key');
    apiKey.value = key || '';

    // 加载客户端信息
    const cId = await getConfig('client_id');
    clientId.value = cId || '';

    const cName = await getConfig('client_name');
    clientName.value = cName || '';

    // 加载挖矿特征
    const features = await getRansomwareFeatures();
    renderFeatures(features);

    // 加载自动上传设置
    const autoUpload = await getConfig('auto_upload');
    if (autoUploadSwitch) {
      autoUploadSwitch.checked = autoUpload === '1';
    }

    // 加载多线程扫描设置
    const useMultiThread = await getConfig('use_multi_thread');
    if (useMultiThreadSwitch) {
      useMultiThreadSwitch.checked = useMultiThread === '1';
    }

    // 加载线程数设置
    const threadCount = await getConfig('thread_count');
    if (threadCountInput) {
      threadCountInput.value = threadCount || Math.min(require('os').cpus().length, 8);
    }

    // 加载测绘设置
    await loadMappingSettings();
  } catch (err) {
    console.error('加载设置出错:', err.message);
    showDialog({
      title: '错误',
      content: `加载设置出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 渲染勒索特征
function renderFeatures(features) {
  featureList.innerHTML = '';

  if (features.length === 0) {
    featureList.innerHTML = '<div class="feature-item">暂无特征</div>';
    return;
  }

  features.forEach(feature => {
    const item = document.createElement('div');
    item.className = 'feature-item';

    // 确定匹配类型显示文本
    const type = feature.match_type || 'exact';
    const typeDisplay = type === 'exact' ? '精确匹配' : '正则匹配';
    const typeClass = type;

    // 确定同步状态显示
    const syncStatus = feature.is_synced ?
      (feature.remote_id ? `已同步 (ID: ${feature.remote_id})` : '已同步') :
      '未同步';
    const syncClass = feature.is_synced ? 'synced' : 'unsynced';

    item.innerHTML = `
      <div class="feature-info">
        <span class="feature-text">${feature.feature_text}</span>
        <div class="feature-details">
          <span class="feature-type ${typeClass}">${typeDisplay}</span>
          <span class="feature-sync-status ${syncClass}">${syncStatus}</span>
        </div>
      </div>
      <div class="feature-actions">
        ${!feature.is_synced ? `<span class="feature-upload" data-id="${feature.id}" title="上传到服务器">↑</span>` : ''}
        <span class="feature-edit" data-id="${feature.id}" title="编辑特征">✎</span>
        <span class="feature-delete" data-id="${feature.id}" title="删除特征">×</span>
      </div>
    `;

    featureList.appendChild(item);
  });

  // 绑定删除特征事件
  document.querySelectorAll('.feature-delete').forEach(btn => {
    btn.addEventListener('click', async () => {
      const id = parseInt(btn.dataset.id);

      showDialog({
        title: '确认',
        content: '确定要删除此挖矿特征吗？',
        buttons: [
          {
            text: '取消',
            className: 'secondary-btn',
            closeDialog: true
          },
          {
            text: '确定',
            className: 'danger-btn',
            onClick: async () => {
              try {
                // 获取特征信息，检查是否需要从远程服务器删除
                const features = await getRansomwareFeatures();
                const feature = features.find(f => f.id === id);

                // 如果特征已同步且有远程ID，则尝试从远程服务器删除
                if (feature && feature.is_synced && feature.remote_id) {
                  const apiUrl = await getConfig('api_server');
                  const apiKey = await getConfig('api_key');

                  if (apiUrl && apiKey) {
                    try {
                      await deleteRemoteFeature(apiUrl, apiKey, feature.remote_id);
                      // 即使远程删除失败，我们仍然继续删除本地特征
                    } catch (err) {
                      console.error('删除远程特征失败:', err.message);
                      // 继续删除本地特征
                    }
                  }
                }

                // 删除本地特征
                await deleteRansomwareFeature(id);

                showDialog({
                  title: '成功',
                  content: '已删除挖矿特征',
                  buttons: [{
                    text: '确定',
                    onClick: () => {
                      loadSettings(); // 重新加载特征列表
                    },
                    closeDialog: true
                  }]
                });
              } catch (err) {
                console.error('删除特征失败:', err.message);
                showDialog({
                  title: '错误',
                  content: `删除特征失败: ${err.message}`,
                  buttons: [{
                    text: '确定',
                    closeDialog: true
                  }]
                });
              }
            },
            closeDialog: true
          }
        ]
      });
    });
  });

  // 绑定编辑特征事件
  document.querySelectorAll('.feature-edit').forEach(btn => {
    btn.addEventListener('click', async () => {
      const id = parseInt(btn.dataset.id);

      // 获取特征信息
      const features = await getRansomwareFeatures();
      const feature = features.find(f => f.id === id);

      if (!feature) return;

      // 创建编辑对话框
      showDialog({
        title: '编辑特征',
        content: `
          <div class="feature-edit-form">
            <div class="form-group">
              <label for="edit-feature-text">特征文本</label>
              <input type="text" id="edit-feature-text" value="${feature.feature_text}" />
            </div>
            <div class="form-group">
              <label for="edit-match-type">匹配类型</label>
              <select id="edit-match-type">
                <option value="exact" ${feature.match_type === 'exact' ? 'selected' : ''}>精确匹配</option>
                <option value="regex" ${feature.match_type === 'regex' ? 'selected' : ''}>正则匹配</option>
              </select>
            </div>
          </div>
        `,
        buttons: [
          {
            text: '取消',
            className: 'secondary-btn',
            closeDialog: true
          },
          {
            text: '保存',
            className: 'primary-btn',
            onClick: async () => {
              try {
                const newFeatureText = document.getElementById('edit-feature-text').value.trim();
                const newMatchType = document.getElementById('edit-match-type').value;

                if (!newFeatureText) {
                  showDialog({
                    title: '错误',
                    content: '特征文本不能为空',
                    buttons: [{
                      text: '确定',
                      closeDialog: true
                    }]
                  });
                  return;
                }

                // 如果是正则表达式，先验证是否有效
                if (newMatchType === 'regex') {
                  try {
                    new RegExp(newFeatureText);
                  } catch (e) {
                    showDialog({
                      title: '错误',
                      content: `无效的正则表达式: ${e.message}`,
                      buttons: [{
                        text: '确定',
                        closeDialog: true
                      }]
                    });
                    return;
                  }
                }

                // 更新本地特征
                await updateRansomwareFeature(
                  id,
                  newFeatureText,
                  newMatchType,
                  feature.remote_id,
                  feature.is_synced ? 1 : 0
                );

                // 如果特征已同步且有远程ID，则尝试更新远程服务器
                if (feature.is_synced && feature.remote_id) {
                  const apiUrl = await getConfig('api_server');
                  const apiKey = await getConfig('api_key');

                  if (apiUrl && apiKey) {
                    try {
                      await updateFeature(apiUrl, apiKey, feature.remote_id, {
                        feature_text: newFeatureText,
                        match_type: newMatchType
                      });
                    } catch (err) {
                      console.error('更新远程特征失败:', err.message);
                      // 继续，即使远程更新失败
                    }
                  }
                }

                showDialog({
                  title: '成功',
                  content: '特征已更新',
                  buttons: [{
                    text: '确定',
                    onClick: () => {
                      loadSettings(); // 重新加载特征列表
                    },
                    closeDialog: true
                  }]
                });
              } catch (err) {
                console.error('更新特征失败:', err.message);
                showDialog({
                  title: '错误',
                  content: `更新特征失败: ${err.message}`,
                  buttons: [{
                    text: '确定',
                    closeDialog: true
                  }]
                });
              }
            },
            closeDialog: true
          }
        ]
      });
    });
  });

  // 绑定上传特征事件
  document.querySelectorAll('.feature-upload').forEach(btn => {
    btn.addEventListener('click', async () => {
      const id = parseInt(btn.dataset.id);

      // 获取特征信息
      const features = await getRansomwareFeatures();
      const feature = features.find(f => f.id === id);

      if (!feature) return;

      // 获取API配置
      const apiUrl = await getConfig('api_server');
      const apiKey = await getConfig('api_key');

      if (!apiUrl || !apiKey) {
        showDialog({
          title: '错误',
          content: '请先配置API服务器地址和API密钥',
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
        return;
      }

      // 显示上传中对话框
      showDialog({
        title: '上传中',
        content: '正在上传特征到远程服务器...',
        closable: false
      });

      try {
        // 上传特征到服务器
        const result = await uploadFeature(apiUrl, apiKey, {
          feature_text: feature.feature_text,
          match_type: feature.match_type
        });

        if (result.success) {
          // 更新本地特征的同步状态
          await markFeatureAsSynced(id, result.feature.id);

          showDialog({
            title: '成功',
            content: '特征已上传到服务器',
            buttons: [{
              text: '确定',
              onClick: () => {
                loadSettings(); // 重新加载特征列表
              },
              closeDialog: true
            }]
          });
        } else {
          showDialog({
            title: '上传失败',
            content: result.message,
            buttons: [{
              text: '确定',
              closeDialog: true
            }]
          });
        }
      } catch (err) {
        console.error('上传特征失败:', err.message);
        showDialog({
          title: '错误',
          content: `上传特征失败: ${err.message}`,
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
      }
    });
  });
}

// 添加勒索特征
addFeatureBtn.addEventListener('click', async () => {
  const feature = newFeature.value.trim();
  const type = matchType.value;

  if (!feature) {
    showDialog({
      title: '提示',
      content: '请输入特征',
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
    return;
  }

  // 如果是正则表达式，先验证是否有效
  if (type === 'regex') {
    try {
      new RegExp(feature);
    } catch (e) {
      showDialog({
        title: '错误',
        content: `无效的正则表达式: ${e.message}`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }
  }

  try {
    // 保存到本地
    const featureId = await saveRansomwareFeature(feature, type);
    newFeature.value = '';
    matchType.value = 'exact'; // 重置为精确匹配

    // 询问是否同时上传到服务器
    showDialog({
      title: '特征已添加',
      content: '是否同时上传到远程服务器？',
      buttons: [
        {
          text: '否',
          className: 'secondary-btn',
          onClick: () => {
            loadSettings(); // 重新加载特征列表
          },
          closeDialog: true
        },
        {
          text: '是',
          className: 'primary-btn',
          onClick: async () => {
            // 获取API配置
            const apiUrl = await getConfig('api_server');
            const apiKey = await getConfig('api_key');

            if (!apiUrl || !apiKey) {
              showDialog({
                title: '错误',
                content: '请先配置API服务器地址和API密钥',
                buttons: [{
                  text: '确定',
                  onClick: () => {
                    loadSettings(); // 重新加载特征列表
                  },
                  closeDialog: true
                }]
              });
              return;
            }

            // 显示上传中对话框
            showDialog({
              title: '上传中',
              content: '正在上传特征到远程服务器...',
              closable: false
            });

            try {
              // 上传特征到服务器
              const result = await uploadFeature(apiUrl, apiKey, {
                feature_text: feature,
                match_type: type
              });

              if (result.success) {
                // 更新本地特征的同步状态
                await markFeatureAsSynced(featureId, result.feature.id);

                showDialog({
                  title: '成功',
                  content: '特征已上传到服务器',
                  buttons: [{
                    text: '确定',
                    onClick: () => {
                      loadSettings(); // 重新加载特征列表
                    },
                    closeDialog: true
                  }]
                });
              } else {
                showDialog({
                  title: '上传失败',
                  content: result.message,
                  buttons: [{
                    text: '确定',
                    onClick: () => {
                      loadSettings(); // 重新加载特征列表
                    },
                    closeDialog: true
                  }]
                });
              }
            } catch (err) {
              console.error('上传特征失败:', err.message);
              showDialog({
                title: '错误',
                content: `上传特征失败: ${err.message}`,
                buttons: [{
                  text: '确定',
                  onClick: () => {
                    loadSettings(); // 重新加载特征列表
                  },
                  closeDialog: true
                }]
              });
            }
          },
          closeDialog: true
        }
      ]
    });
  } catch (err) {
    console.error('添加特征出错:', err.message);
    showDialog({
      title: '错误',
      content: `添加特征出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
});

// 保存设置
saveSettingsBtn.addEventListener('click', async () => {
  try {
    // 检查API服务器地址格式
    const serverUrl = apiServer.value.trim();
    if (serverUrl && !serverUrl.startsWith('http://') && !serverUrl.startsWith('https://')) {
      showDialog({
        title: '提示',
        content: 'API服务器地址应以 http:// 或 https:// 开头',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 保存所有配置
    await updateConfig('api_server', serverUrl);
    await updateConfig('api_key', apiKey.value.trim());
    await updateConfig('client_id', clientId.value.trim());
    await updateConfig('client_name', clientName.value.trim());

    // 保存自动上传设置
    if (autoUploadSwitch) {
      await updateConfig('auto_upload', autoUploadSwitch.checked ? '1' : '0');
    }

    // 保存多线程扫描设置
    if (useMultiThreadSwitch) {
      await updateConfig('use_multi_thread', useMultiThreadSwitch.checked ? '1' : '0');
    }

    // 保存线程数设置
    if (threadCountInput) {
      const threadCount = parseInt(threadCountInput.value) || Math.min(require('os').cpus().length, 8);
      // 限制线程数在1-16之间
      const validThreadCount = Math.max(1, Math.min(16, threadCount));
      await updateConfig('thread_count', validThreadCount.toString());
      threadCountInput.value = validThreadCount; // 更新输入框显示
    }

    // 保存Quake Token设置
    if (quakeTokenSettingInput) {
      const tokenValue = quakeTokenSettingInput.value.trim();
      await updateConfig('quake_token', tokenValue);
      // 同步到测绘页面的输入框
      if (quakeTokenInput) {
        quakeTokenInput.value = tokenValue;
      }

      // 如果Token不为空，自动验证并更新用户信息
      if (tokenValue) {
        try {
          const result = await getUserInfo(tokenValue);
          if (result.success) {
            const userData = result.data;
            if (userInfoSpan) userInfoSpan.textContent = `用户: ${userData.user.username}`;
            if (creditInfoSpan) creditInfoSpan.textContent = `积分: ${userData.credit}`;
            if (persistentCreditInfoSpan) persistentCreditInfoSpan.textContent = `长效积分: ${userData.persistent_credit || 0}`;
          }
        } catch (error) {
          console.error('自动验证Token失败:', error.message);
        }
      }
    }

    showDialog({
      title: '成功',
      content: '保存成功',
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } catch (err) {
    console.error('保存设置出错:', err.message);
    showDialog({
      title: '错误',
      content: `保存设置出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
});

// 同步特征
if (syncFeaturesBtn) {
  syncFeaturesBtn.addEventListener('click', async () => {
    try {
      const apiUrl = await getConfig('api_server');
      const apiKeyValue = await getConfig('api_key');

      if (!apiUrl) {
        showDialog({
          title: '提示',
          content: '请先配置API服务器地址',
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
        return;
      }

      if (!apiKeyValue) {
        showDialog({
          title: '提示',
          content: '请先配置API密钥',
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
        return;
      }

      showDialog({
        title: '同步中',
        content: '正在从远程服务器获取挖矿特征...',
        closable: false
      });

      const result = await fetchFeatures(apiUrl, apiKeyValue);

      if (result.success) {
        // 同步特征到本地数据库（保留本地特征）
        await syncFeaturesFromRemote(result.features);

        // 获取未同步的特征数量
        const unsyncedFeatures = await getUnsyncedFeatures();

        showDialog({
          title: '成功',
          content: `成功同步 ${result.features.length} 个挖矿特征，本地还有 ${unsyncedFeatures.length} 个未同步特征`,
          buttons: [{
            text: '确定',
            onClick: () => {
              loadSettings(); // 重新加载特征列表
            },
            closeDialog: true
          }]
        });
      } else {
        showDialog({
          title: '失败',
          content: result.message,
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
      }
    } catch (err) {
      console.error('同步特征出错:', err.message);
      showDialog({
        title: '错误',
        content: `同步特征出错: ${err.message}`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  });
}

// 显示详情
function showDetails(id) {
  const item = currentResults.find(r => r.id === id);

  if (!item) return;

  currentDetailId = id;

  detailHost.textContent = item.host;
  detailPort.textContent = item.port;
  detailStatus.textContent = item.status === 1 ? '成功' : '失败';
  detailTime.textContent = formatDate(item.connect_time);
  detailInfo.textContent = item.status_info;
  detailRansomware.textContent = item.is_ransomware === 1 ? '是' : '否';

  // 显示键值数据
  renderKeyValues(item.key_values);

  // 显示模态框
  detailsModal.style.display = 'block';
}

// 渲染键值数据
function renderKeyValues(keyValues) {
  keyValueData.innerHTML = '';

  // 确保keyValues是对象
  if (!keyValues || typeof keyValues !== 'object' || Object.keys(keyValues).length === 0) {
    keyValueData.innerHTML = '<div class="no-data">无数据</div>';
    return;
  }

  // 如果keyValues是字符串形式的JSON，尝试解析
  if (typeof keyValues === 'string') {
    try {
      keyValues = JSON.parse(keyValues);
    } catch (e) {
      console.error('解析键值数据失败:', e.message);
      keyValueData.innerHTML = '<div class="no-data">无效的JSON数据</div>';
      return;
    }
  }

  // 遍历每个数据库
  for (const dbName in keyValues) {
    const dbSection = document.createElement('div');
    dbSection.className = 'db-section';

    dbSection.innerHTML = `<div class="db-header">${dbName}</div>`;

    const dbKeys = keyValues[dbName];

    // 确保dbKeys是对象
    if (!dbKeys || typeof dbKeys !== 'object' || Object.keys(dbKeys).length === 0) {
      dbSection.innerHTML += '<div class="no-data">无数据</div>';
    } else {
      // 遍历数据库中的每个键
      for (const keyName in dbKeys) {
        const keyData = dbKeys[keyName];
        const keyItem = document.createElement('div');
        keyItem.className = 'key-item';

        let valueHtml = '';
        let typeDisplay = '未知';

        // 确保keyData是对象
        if (keyData && typeof keyData === 'object') {
          typeDisplay = keyData.type || '未知';

          // 处理不同类型的值
          if (typeof keyData.value === 'string') {
            valueHtml = `<pre>${escapeHtml(keyData.value)}</pre>`;
          } else if (Array.isArray(keyData.value)) {
            valueHtml = `<pre>${escapeHtml(JSON.stringify(keyData.value, null, 2))}</pre>`;
          } else if (typeof keyData.value === 'object' && keyData.value !== null) {
            valueHtml = `<pre>${escapeHtml(JSON.stringify(keyData.value, null, 2))}</pre>`;
          } else {
            valueHtml = `<pre>${escapeHtml(String(keyData.value !== undefined ? keyData.value : ''))}</pre>`;
          }
        } else {
          // 如果keyData不是预期的格式，直接显示原始数据
          valueHtml = `<pre>${escapeHtml(JSON.stringify(keyData, null, 2))}</pre>`;
        }

        keyItem.innerHTML = `
          <div class="key-name">${keyName}</div>
          <div class="key-type">类型: ${typeDisplay}</div>
          <div class="key-value">${valueHtml}</div>
        `;

        dbSection.appendChild(keyItem);
      }
    }

    keyValueData.appendChild(dbSection);
  }
}

// 关闭模态框
closeModal.addEventListener('click', () => {
  detailsModal.style.display = 'none';
});

// 点击模态框外部关闭
window.addEventListener('click', (event) => {
  if (event.target === detailsModal) {
    detailsModal.style.display = 'none';
  }
});

// 标记为勒索
markRansomwareBtn.addEventListener('click', async () => {
  if (!currentDetailId) return;

  try {
    await updateRansomwareStatus(currentDetailId, true);
    detailRansomware.textContent = '是';
    loadResults();
    showDialog({
      title: '成功',
      content: '已标记为挖矿',
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } catch (err) {
    console.error('标记挖矿出错:', err.message);
    showDialog({
      title: '错误',
      content: `标记挖矿出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
});

// 取消勒索标记
unmarkRansomwareBtn.addEventListener('click', async () => {
  if (!currentDetailId) return;

  try {
    await updateRansomwareStatus(currentDetailId, false);
    detailRansomware.textContent = '否';
    loadResults();
    showDialog({
      title: '成功',
      content: '已取消标记',
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } catch (err) {
    console.error('取消标记出错:', err.message);
    showDialog({
      title: '错误',
      content: `取消标记出错: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
});

// 格式化日期
function formatDate(dateStr) {
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch (err) {
    return dateStr;
  }
}

// 转义HTML
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// 上传单个扫描结果
async function uploadSingleScanResult(resultId) {
  try {
    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');
    const clientId = await getConfig('client_id');
    const clientName = await getConfig('client_name');

    if (!apiUrl || !apiKey) {
      showDialog({
        title: '错误',
        content: '请先配置API服务器地址和API密钥',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return false;
    }

    // 获取结果详情
    const results = await getScanResults();
    const result = results.find(r => r.id === resultId);

    if (!result) {
      showDialog({
        title: '错误',
        content: '未找到指定结果',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return false;
    }

    // 显示上传中对话框
    showDialog({
      title: '上传中',
      content: '正在上传扫描结果到远程服务器...',
      closable: false
    });

    // 上传结果
    const uploadResult = await uploadSingleResult(apiUrl, result, apiKey, clientId, clientName);

    if (uploadResult.success) {
      // 标记为已上传
      await markResultsAsUploaded([resultId]);

      showDialog({
        title: '成功',
        content: '结果已上传到服务器',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return true;
    } else {
      showDialog({
        title: '上传失败',
        content: uploadResult.message,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return false;
    }
  } catch (err) {
    console.error('上传结果失败:', err.message);
    showDialog({
      title: '错误',
      content: `上传结果失败: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
    return false;
  }
}

// 上传选中的扫描结果
async function uploadSelectedResults() {
  try {
    // 获取选中的结果ID
    const selectedIds = Array.from(document.querySelectorAll('.select-result:checked'))
      .map(cb => parseInt(cb.dataset.id));

    if (selectedIds.length === 0) {
      showDialog({
        title: '提示',
        content: '请先选择要上传的结果',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');
    const clientId = await getConfig('client_id');
    const clientName = await getConfig('client_name');

    if (!apiUrl || !apiKey) {
      showDialog({
        title: '错误',
        content: '请先配置API服务器地址和API密钥',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 获取选中的结果
    const allResults = await getScanResults();
    const selectedResults = allResults.filter(r => selectedIds.includes(r.id));

    // 显示上传中对话框
    showDialog({
      title: '上传中',
      content: `正在上传 ${selectedResults.length} 个扫描结果到远程服务器...`,
      closable: false
    });

    // 上传结果
    const uploadResult = await uploadResults(apiUrl, selectedResults, apiKey, clientId, clientName);

    if (uploadResult.success) {
      // 标记为已上传
      await markResultsAsUploaded(selectedIds);

      showDialog({
        title: '成功',
        content: `成功上传 ${uploadResult.uploaded} 个结果到服务器`,
        buttons: [{
          text: '确定',
          onClick: () => {
            loadResults(); // 重新加载结果以更新状态
          },
          closeDialog: true
        }]
      });
    } else {
      showDialog({
        title: '上传失败',
        content: uploadResult.message,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (err) {
    console.error('上传结果失败:', err.message);
    showDialog({
      title: '错误',
      content: `上传结果失败: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 上传所有未上传的扫描结果
async function uploadAllUnuploadedResults() {
  try {
    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');
    const clientId = await getConfig('client_id');
    const clientName = await getConfig('client_name');

    if (!apiUrl || !apiKey) {
      showDialog({
        title: '错误',
        content: '请先配置API服务器地址和API密钥',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 获取所有未上传的结果
    const unuploadedResults = await getUnuploadedResults();

    if (unuploadedResults.length === 0) {
      showDialog({
        title: '提示',
        content: '当前没有未上传的结果',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 显示上传中对话框
    showDialog({
      title: '上传中',
      content: `正在上传 ${unuploadedResults.length} 个扫描结果到远程服务器...`,
      closable: false,
      showProgress: true
    });

    // 初始化进度条
    dialogProgressFill.style.width = '0%';
    dialogProgressText.textContent = '准备上传...';

    // 分批上传，每批100条
    const batchSize = 100;
    let uploadedCount = 0;
    let successCount = 0;

    for (let i = 0; i < unuploadedResults.length; i += batchSize) {
      const batch = unuploadedResults.slice(i, i + batchSize);

      // 更新进度
      const progress = Math.floor((i / unuploadedResults.length) * 100);
      dialogProgressFill.style.width = `${progress}%`;
      dialogProgressText.textContent = `正在上传... ${i}/${unuploadedResults.length}`;

      // 上传当前批次
      const uploadResult = await uploadResults(apiUrl, batch, apiKey, clientId, clientName);

      if (uploadResult.success) {
        // 标记为已上传
        const ids = batch.map(r => r.id);
        await markResultsAsUploaded(ids);
        uploadedCount += batch.length;
        successCount += uploadResult.uploaded || 0;
      }
    }

    // 更新进度为100%
    dialogProgressFill.style.width = '100%';
    dialogProgressText.textContent = '上传完成';

    showDialog({
      title: '成功',
      content: `成功上传 ${successCount}/${uploadedCount} 个结果到服务器`,
      buttons: [{
        text: '确定',
        onClick: () => {
          loadResults(); // 重新加载结果以更新状态
        },
        closeDialog: true
      }]
    });
  } catch (err) {
    console.error('上传结果失败:', err.message);
    showDialog({
      title: '错误',
      content: `上传结果失败: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 自动上传扫描结果
async function autoUploadScanResults(results) {
  try {
    // 检查是否启用自动上传
    const autoUpload = await getConfig('auto_upload');
    if (autoUpload !== '1') {
      return;
    }

    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');
    const clientId = await getConfig('client_id');
    const clientName = await getConfig('client_name');

    if (!apiUrl || !apiKey) {
      console.error('自动上传失败: 未配置API服务器地址或API密钥');
      return;
    }

    // 上传结果
    const uploadResult = await uploadResults(apiUrl, results, apiKey, clientId, clientName);

    if (uploadResult.success) {
      // 标记为已上传
      const ids = results.map(r => r.id);
      await markResultsAsUploaded(ids);
      console.log(`自动上传成功: 已上传 ${uploadResult.uploaded} 个结果`);
    } else {
      console.error('自动上传失败:', uploadResult.message);
    }
  } catch (err) {
    console.error('自动上传失败:', err.message);
  }
}

// 同步远程扫描结果
async function syncRemoteScanResults() {
  try {
    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');

    if (!apiUrl || !apiKey) {
      showDialog({
        title: '错误',
        content: '请先配置API服务器地址和API密钥',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 显示同步中对话框
    showDialog({
      title: '同步中',
      content: '正在从远程服务器获取扫描结果...',
      closable: false,
      showProgress: true
    });

    // 初始化进度条
    dialogProgressFill.style.width = '0%';
    dialogProgressText.textContent = '正在获取第1页数据...';

    // 获取所有分页数据
    let allResults = [];
    let currentPage = 1;
    let hasMoreData = true;
    const pageSize = 100;
    let totalSyncedCount = 0;

    while (hasMoreData) {
      const offset = (currentPage - 1) * pageSize;

      // 更新进度提示
      dialogProgressText.textContent = `正在获取第${currentPage}页数据...`;

      // 获取当前页的数据
      const fetchResult = await fetchScanResults(apiUrl, apiKey, pageSize, offset);

      if (fetchResult.success) {
        // 如果有结果数据
        if (fetchResult.results && fetchResult.results.length > 0) {
          allResults = allResults.concat(fetchResult.results);

          // 如果返回的数据少于页大小，说明没有更多数据了
          if (fetchResult.results.length < pageSize) {
            hasMoreData = false;
          }

          // 更新对话框内容
          dialogContent.innerHTML = `已获取 ${allResults.length} 条远程扫描结果，正在同步到本地...`;

          // 更新进度条
          const progressPercent = Math.min(currentPage * 30, 90); // 最多到90%，留10%给同步操作
          dialogProgressFill.style.width = `${progressPercent}%`;

          // 每获取一页数据后，就同步到本地数据库
          const syncResult = await syncScanResultsFromRemote(fetchResult.results);
          if (syncResult.success) {
            totalSyncedCount += syncResult.syncedCount;
          }

          currentPage++;
        } else {
          // 没有数据了
          hasMoreData = false;
        }
      } else {
        // 获取失败
        showDialog({
          title: '获取失败',
          content: fetchResult.message,
          buttons: [{
            text: '确定',
            closeDialog: true
          }]
        });
        return;
      }
    }

    // 更新进度为100%
    dialogProgressFill.style.width = '100%';
    dialogProgressText.textContent = '同步完成';

    // 显示结果
    showDialog({
      title: '成功',
      content: `成功获取 ${allResults.length} 条远程扫描结果，实际同步 ${totalSyncedCount} 条数据到本地（已去重）。`,
      buttons: [{
        text: '确定',
        onClick: () => {
          loadResults(); // 重新加载结果以更新状态
        },
        closeDialog: true
      }]
    });
  } catch (err) {
    console.error('同步扫描结果失败:', err.message);
    showDialog({
      title: '错误',
      content: `同步扫描结果失败: ${err.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 获取远程扫描统计信息
async function fetchRemoteScanStats() {
  try {
    // 获取API配置
    const apiUrl = await getConfig('api_server');
    const apiKey = await getConfig('api_key');

    if (!apiUrl || !apiKey) {
      return { success: false, message: '未配置API服务器地址或API密钥' };
    }

    return await fetchScanStats(apiUrl, apiKey);
  } catch (err) {
    console.error('获取远程扫描统计失败:', err.message);
    return {
      success: false,
      message: `获取远程扫描统计失败: ${err.message}`,
      stats: {}
    };
  }
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
  // 默认选择单个扫描
  scanTypeSelect.value = 'single';
  singleInput.style.display = 'block';
  batchInput.style.display = 'none';

  // 绑定全选/取消全选事件
  const selectAllCheckbox = document.getElementById('select-all-results');
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', () => {
      const checked = selectAllCheckbox.checked;
      document.querySelectorAll('.select-result:not([disabled])').forEach(cb => {
        cb.checked = checked;
      });
    });
  }

  // 绑定上传结果按钮事件
  if (uploadResultsBtn) {
    uploadResultsBtn.addEventListener('click', () => {
      // 弹出选择菜单
      showDialog({
        title: '上传结果',
        content: '请选择上传方式',
        buttons: [
          {
            text: '取消',
            className: 'secondary-btn',
            closeDialog: true
          },
          {
            text: '上传选中',
            className: 'primary-btn',
            onClick: () => {
              uploadSelectedResults();
            },
            closeDialog: true
          },
          {
            text: '上传所有未上传',
            className: 'primary-btn',
            onClick: () => {
              uploadAllUnuploadedResults();
            },
            closeDialog: true
          }
        ]
      });
    });
  }

  // 绑定同步结果按钮事件
  const syncResultsBtn = document.getElementById('sync-results');
  if (syncResultsBtn) {
    syncResultsBtn.addEventListener('click', syncRemoteScanResults);
  }

  // 绑定测绘相关事件
  initMappingEvents();
});

// ==================== 测绘模块相关功能 ====================

let currentMappingTask = null;
let mappingResults = [];
let mappingCurrentPage = 1;
const mappingPageSize = 50;

// 初始化测绘事件
function initMappingEvents() {
  // 验证Token按钮
  if (checkTokenBtn) {
    checkTokenBtn.addEventListener('click', checkQuakeToken);
  }

  // 开始测绘按钮
  if (startMappingBtn) {
    startMappingBtn.addEventListener('click', startMapping);
  }

  // 自动测绘按钮
  if (autoMappingBtn) {
    autoMappingBtn.addEventListener('click', startAutoMapping);
  }

  // 停止测绘按钮
  if (stopMappingBtn) {
    stopMappingBtn.addEventListener('click', stopMapping);
  }

  // 清空结果按钮
  if (clearMappingBtn) {
    clearMappingBtn.addEventListener('click', clearMappingResultsDialog);
  }

  // 导出目标按钮
  if (exportTargetsBtn) {
    exportTargetsBtn.addEventListener('click', exportMappingTargets);
  }

  // 导出未测试按钮
  if (exportUntestedBtn) {
    exportUntestedBtn.addEventListener('click', exportUntestedTargets);
  }

  // 发送未测试到扫描按钮
  if (sendUntestedBtn) {
    sendUntestedBtn.addEventListener('click', sendUntestedToScan);
  }

  // 测绘结果翻页按钮
  const mappingPrevBtn = document.getElementById('mapping-prev-btn');
  const mappingNextBtn = document.getElementById('mapping-next-btn');

  if (mappingPrevBtn) {
    mappingPrevBtn.addEventListener('click', () => {
      if (mappingCurrentPage > 1) {
        mappingCurrentPage--;
        renderMappingTable();
      }
    });
  }

  if (mappingNextBtn) {
    mappingNextBtn.addEventListener('click', () => {
      const totalPages = Math.ceil(mappingResults.length / mappingPageSize);
      if (mappingCurrentPage < totalPages) {
        mappingCurrentPage++;
        renderMappingTable();
      }
    });
  }

  // 同步Token输入框
  if (quakeTokenInput && quakeTokenSettingInput) {
    quakeTokenInput.addEventListener('input', () => {
      quakeTokenSettingInput.value = quakeTokenInput.value;
    });

    quakeTokenSettingInput.addEventListener('input', () => {
      quakeTokenInput.value = quakeTokenSettingInput.value;
    });
  }

  // 加载测绘设置和结果
  loadMappingSettings();
  loadMappingResults();
}

// 验证Quake Token
async function checkQuakeToken() {
  try {
    const token = quakeTokenInput.value.trim();

    if (!token) {
      showDialog({
        title: '错误',
        content: '请输入Quake API Token',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 显示验证中
    checkTokenBtn.disabled = true;
    checkTokenBtn.textContent = '验证中...';

    // 调用用户信息接口验证Token
    const result = await getUserInfo(token);

    if (result.success) {
      const userData = result.data;

      // 更新用户信息显示
      userInfoSpan.textContent = `用户: ${userData.user.username}`;
      creditInfoSpan.textContent = `积分: ${userData.credit}`;
      persistentCreditInfoSpan.textContent = `长效积分: ${userData.persistent_credit || 0}`;

      // 保存Token到配置
      await updateConfig('quake_token', token);

      // 同步到设置页面的输入框
      if (quakeTokenSettingInput) {
        quakeTokenSettingInput.value = token;
      }

      await showDialog({
        title: '验证成功',
        content: `Token验证成功！\n用户: ${userData.user.username}\n剩余积分: ${userData.credit}\n长效积分: ${userData.persistent_credit || 0}`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    } else {
      userInfoSpan.textContent = '未登录';
      creditInfoSpan.textContent = '积分: --';
      persistentCreditInfoSpan.textContent = '长效积分: --';

      showDialog({
        title: '验证失败',
        content: result.error,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('验证Token失败:', error.message);
    showDialog({
      title: '错误',
      content: `验证Token失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } finally {
    checkTokenBtn.disabled = false;
    checkTokenBtn.textContent = '验证Token';
  }
}

// 开始测绘
async function startMapping() {
  try {
    const token = quakeTokenInput.value.trim();
    const mappingType = mappingModeSelect.value;
    const maxResults = parseInt(maxResultsInput.value) || 1000;
    const batchSize = parseInt(batchSizeInput.value) || 100;
    const startTime = startTimeInput.value ? formatTimeForQuake(new Date(startTimeInput.value)) : null;
    const endTime = endTimeInput.value ? formatTimeForQuake(new Date(endTimeInput.value)) : null;

    if (!token) {
      showDialog({
        title: '错误',
        content: '请先输入并验证Quake API Token',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    if (!MAPPING_MODES[mappingType]) {
      showDialog({
        title: '错误',
        content: '无效的测绘模式',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 验证时间范围
    const timeValidation = validateTimeRange(startTime, endTime);
    if (!timeValidation.valid) {
      showDialog({
        title: '时间范围错误',
        content: timeValidation.error,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 更新UI状态
    startMappingBtn.disabled = true;
    autoMappingBtn.disabled = true;
    stopMappingBtn.disabled = false;
    mappingProgressContainer.style.display = 'block';
    mappingProgressFill.style.width = '0%';
    mappingProgressText.textContent = '准备测绘...';

    // 进度回调
    const onProgress = (completed, total) => {
      const percent = total > 0 ? Math.floor((completed / total) * 100) : 0;
      mappingProgressFill.style.width = `${percent}%`;
      mappingProgressText.textContent = `已获取 ${completed}/${total} 条数据`;
    };

    // 批次完成回调
    const onBatchComplete = (batchResults) => {
      // 实时更新结果显示
      loadMappingResults();
    };

    // 执行测绘任务
    const result = await executeMappingTask(token, mappingType, {
      maxResults,
      batchSize,
      onProgress,
      onBatchComplete,
      startTime,
      endTime,
      autoMapping: false
    });

    if (result.success) {
      mappingProgressFill.style.width = '100%';
      mappingProgressText.textContent = `测绘完成，共获取 ${result.totalResults} 条数据`;

      // 刷新结果显示
      loadMappingResults();

      showDialog({
        title: '测绘完成',
        content: `成功获取 ${result.totalResults} 条Redis服务数据`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    } else {
      mappingProgressText.textContent = `测绘失败: ${result.error}`;

      showDialog({
        title: '测绘失败',
        content: result.error,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('测绘失败:', error.message);
    mappingProgressText.textContent = `测绘失败: ${error.message}`;

    showDialog({
      title: '错误',
      content: `测绘失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } finally {
    // 恢复UI状态
    startMappingBtn.disabled = false;
    autoMappingBtn.disabled = false;
    stopMappingBtn.disabled = true;
    setTimeout(() => {
      mappingProgressContainer.style.display = 'none';
    }, 3000);
  }
}

// 自动测绘
async function startAutoMapping() {
  try {
    const token = quakeTokenInput.value.trim();
    const mappingType = mappingModeSelect.value;
    const maxResults = parseInt(maxResultsInput.value) || 1000;
    const batchSize = parseInt(batchSizeInput.value) || 100;

    if (!token) {
      showDialog({
        title: '错误',
        content: '请先输入并验证Quake API Token',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    if (!MAPPING_MODES[mappingType]) {
      showDialog({
        title: '错误',
        content: '无效的测绘模式',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 更新UI状态
    startMappingBtn.disabled = true;
    autoMappingBtn.disabled = true;
    stopMappingBtn.disabled = false;
    mappingProgressContainer.style.display = 'block';
    mappingProgressFill.style.width = '0%';
    mappingProgressText.textContent = '准备自动测绘...';

    // 进度回调
    const onProgress = (completed, total) => {
      const percent = total > 0 ? Math.floor((completed / total) * 100) : 0;
      mappingProgressFill.style.width = `${percent}%`;
      mappingProgressText.textContent = `已获取 ${completed}/${total} 条数据`;
    };

    // 批次完成回调
    const onBatchComplete = (batchResults) => {
      // 实时更新结果显示
      loadMappingResults();
    };

    // 执行自动测绘任务
    const result = await executeAutoMapping(token, mappingType, {
      maxResults,
      batchSize,
      onProgress,
      onBatchComplete
    });

    if (result.success) {
      mappingProgressFill.style.width = '100%';
      mappingProgressText.textContent = `自动测绘完成，共获取 ${result.totalResults} 条新数据`;

      // 刷新结果显示
      loadMappingResults();

      showDialog({
        title: '自动测绘完成',
        content: `成功获取 ${result.totalResults} 条新的Redis服务数据`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    } else {
      mappingProgressText.textContent = `自动测绘失败: ${result.error}`;

      showDialog({
        title: '自动测绘失败',
        content: result.error,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('自动测绘失败:', error.message);
    mappingProgressText.textContent = `自动测绘失败: ${error.message}`;

    showDialog({
      title: '错误',
      content: `自动测绘失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } finally {
    // 恢复UI状态
    startMappingBtn.disabled = false;
    autoMappingBtn.disabled = false;
    stopMappingBtn.disabled = true;
    setTimeout(() => {
      mappingProgressContainer.style.display = 'none';
    }, 3000);
  }
}

// 停止测绘
function stopMapping() {
  if (currentMappingTask) {
    currentMappingTask.stop = true;
  }

  startMappingBtn.disabled = false;
  autoMappingBtn.disabled = false;
  stopMappingBtn.disabled = true;
  mappingProgressContainer.style.display = 'none';

  showDialog({
    title: '提示',
    content: '测绘任务已停止',
    buttons: [{
      text: '确定',
      closeDialog: true
    }]
  });
}

// 清空测绘结果
async function clearMappingResultsDialog() {
  try {
    const result = await showDialog({
      title: '确认清空',
      content: '确定要清空所有测绘结果吗？此操作不可恢复。',
      buttons: [
        {
          text: '取消',
          className: 'secondary-btn',
          closeDialog: true
        },
        {
          text: '确定清空',
          className: 'danger-btn',
          closeDialog: true,
          returnValue: true
        }
      ]
    });

    if (result) {
      await clearMappingResults();
      loadMappingResults();

      await showDialog({
        title: '成功',
        content: '测绘结果已清空',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('清空测绘结果失败:', error.message);
    await showDialog({
      title: '错误',
      content: `清空测绘结果失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 加载测绘结果
async function loadMappingResults() {
  try {
    const results = await getMappingResults(null, 0); // 获取所有结果
    mappingResults = results;

    // 获取统计信息
    const stats = await getMappingStats();

    // 更新统计信息
    const totalCount = stats.total_count || 0;
    const validCount = results.filter(r => r.ip && r.port).length;
    const testedCount = stats.tested_count || 0;
    const untestedCount = stats.untested_count || 0;
    const latestTime = stats.latest_time ? formatDate(stats.latest_time) : '--';

    mappingTotalCount.textContent = `总数: ${totalCount}`;
    mappingSuccessCount.textContent = `有效: ${validCount}`;
    mappingTestedCount.textContent = `已测试: ${testedCount}`;
    mappingUntestedCount.textContent = `未测试: ${untestedCount}`;
    mappingLatestTime.textContent = `最新: ${latestTime}`;

    // 渲染表格
    renderMappingTable();
  } catch (error) {
    console.error('加载测绘结果失败:', error.message);
    mappingResultsTbody.innerHTML = '<tr><td colspan="8" class="no-data">加载失败</td></tr>';
  }
}

// 渲染测绘结果表格
function renderMappingTable() {
  if (!mappingResults || mappingResults.length === 0) {
    mappingResultsTbody.innerHTML = '<tr><td colspan="8" class="no-data">暂无测绘数据</td></tr>';
    return;
  }

  // 分页处理
  const startIndex = (mappingCurrentPage - 1) * mappingPageSize;
  const endIndex = startIndex + mappingPageSize;
  const pageResults = mappingResults.slice(startIndex, endIndex);

  const rows = pageResults.map(result => {
    const location = [result.country_cn, result.province_cn, result.city_cn]
      .filter(Boolean).join(' ');

    const testStatus = result.is_tested ?
      `<span class="status-tested">已测试</span><br><small>${formatDate(result.tested_time)}</small>` :
      '<span class="status-untested">未测试</span>';

    return `
      <tr>
        <td>${result.ip || '--'}</td>
        <td>${result.port || '--'}</td>
        <td>${location || '--'}</td>
        <td title="${result.org || '--'}">${truncateText(result.org || '--', 20)}</td>
        <td>${result.service_name || '--'}</td>
        <td>${formatDate(result.time) || '--'}</td>
        <td>${testStatus}</td>
        <td>
          <button class="btn btn-small" onclick="addToScanTargets('${result.ip}', ${result.port})">
            添加到扫描
          </button>
        </td>
      </tr>
    `;
  }).join('');

  mappingResultsTbody.innerHTML = rows;

  // 更新分页信息
  const totalPages = Math.ceil(mappingResults.length / mappingPageSize);
  document.getElementById('mapping-page-info').textContent =
    `第 ${mappingCurrentPage} 页，共 ${mappingResults.length} 条`;

  // 更新分页按钮状态
  document.getElementById('mapping-prev-btn').disabled = mappingCurrentPage <= 1;
  document.getElementById('mapping-next-btn').disabled = mappingCurrentPage >= totalPages;
}

// 导出测绘目标
async function exportMappingTargets() {
  try {
    if (!mappingResults || mappingResults.length === 0) {
      showDialog({
        title: '提示',
        content: '暂无测绘数据可导出',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 生成目标列表
    const targets = mappingResults
      .filter(r => r.ip && r.port)
      .map(r => `${r.ip}:${r.port}`)
      .join('\n');

    // 调用Electron的文件保存对话框
    const filePath = await ipcRenderer.invoke('export-data', targets, 'mapping_targets');

    if (filePath) {
      // 写入文件
      const fs = require('fs');
      fs.writeFileSync(filePath.replace('.xlsx', '.txt'), targets, 'utf8');

      showDialog({
        title: '导出成功',
        content: `测绘目标已导出到: ${filePath.replace('.xlsx', '.txt')}`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('导出测绘目标失败:', error.message);
    showDialog({
      title: '错误',
      content: `导出测绘目标失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 导出未测试目标
async function exportUntestedTargets() {
  try {
    const untestedResults = await getUntestedMappingResults();

    if (!untestedResults || untestedResults.length === 0) {
      showDialog({
        title: '提示',
        content: '暂无未测试的测绘数据可导出',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 生成目标列表
    const targets = untestedResults
      .filter(r => r.ip && r.port)
      .map(r => `${r.ip}:${r.port}`)
      .join('\n');

    // 调用Electron的文件保存对话框
    const filePath = await ipcRenderer.invoke('export-data', targets, 'untested_targets');

    if (filePath) {
      // 写入文件
      const fs = require('fs');
      fs.writeFileSync(filePath.replace('.xlsx', '.txt'), targets, 'utf8');

      showDialog({
        title: '导出成功',
        content: `未测试目标已导出到: ${filePath.replace('.xlsx', '.txt')}\n共 ${untestedResults.length} 个目标`,
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
    }
  } catch (error) {
    console.error('导出未测试目标失败:', error.message);
    showDialog({
      title: '错误',
      content: `导出未测试目标失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 发送未测试目标到扫描
async function sendUntestedToScan() {
  try {
    const untestedResults = await getUntestedMappingResults();

    if (!untestedResults || untestedResults.length === 0) {
      showDialog({
        title: '提示',
        content: '暂无未测试的测绘数据',
        buttons: [{
          text: '确定',
          closeDialog: true
        }]
      });
      return;
    }

    // 生成目标列表
    const targets = untestedResults
      .filter(r => r.ip && r.port)
      .map(r => `${r.ip}:${r.port}`)
      .join('\n');

    // 切换到扫描页面
    const scanTab = document.querySelector('[data-tab="scan"]');
    if (scanTab) {
      scanTab.click();
    }

    // 设置为批量扫描模式
    if (scanTypeSelect) {
      scanTypeSelect.value = 'batch';
      scanTypeSelect.dispatchEvent(new Event('change'));
    }

    // 添加目标到文本框
    if (redisTargets) {
      const currentTargets = redisTargets.value.trim();
      if (currentTargets) {
        redisTargets.value = currentTargets + '\n' + targets;
      } else {
        redisTargets.value = targets;
      }
    }

    showDialog({
      title: '成功',
      content: `已将 ${untestedResults.length} 个未测试目标添加到扫描列表`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  } catch (error) {
    console.error('发送未测试目标到扫描失败:', error.message);
    showDialog({
      title: '错误',
      content: `发送未测试目标到扫描失败: ${error.message}`,
      buttons: [{
        text: '确定',
        closeDialog: true
      }]
    });
  }
}

// 添加到扫描目标
function addToScanTargets(ip, port) {
  const target = `${ip}:${port}`;

  // 切换到扫描页面
  const scanTab = document.querySelector('[data-tab="scan"]');
  if (scanTab) {
    scanTab.click();
  }

  // 设置为批量扫描模式
  if (scanTypeSelect) {
    scanTypeSelect.value = 'batch';
    scanTypeSelect.dispatchEvent(new Event('change'));
  }

  // 添加目标到文本框
  if (redisTargets) {
    const currentTargets = redisTargets.value.trim();
    if (currentTargets) {
      redisTargets.value = currentTargets + '\n' + target;
    } else {
      redisTargets.value = target;
    }
  }

  showDialog({
    title: '成功',
    content: `目标 ${target} 已添加到扫描列表`,
    buttons: [{
      text: '确定',
      closeDialog: true
    }]
  });
}

// 截断文本
function truncateText(text, maxLength) {
  if (!text || text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength) + '...';
}

// 在loadSettings函数中添加Quake Token的加载
async function loadMappingSettings() {
  try {
    const quakeToken = await getConfig('quake_token');
    if (quakeToken && quakeTokenInput) {
      quakeTokenInput.value = quakeToken;
    }
    if (quakeToken && quakeTokenSettingInput) {
      quakeTokenSettingInput.value = quakeToken;
    }

    // 如果有Token，自动验证并显示用户信息
    if (quakeToken) {
      const result = await getUserInfo(quakeToken);
      if (result.success) {
        const userData = result.data;
        userInfoSpan.textContent = `用户: ${userData.user.username}`;
        creditInfoSpan.textContent = `积分: ${userData.credit}`;
        persistentCreditInfoSpan.textContent = `长效积分: ${userData.persistent_credit || 0}`;
      }
    }
  } catch (error) {
    console.error('加载测绘设置失败:', error.message);
  }
}