# Redis未授权访问检测工具 - 更新日志

## 版本 2.0.0 - 重大功能更新

### 🚀 新增功能

#### 1. Quake测绘模块
- **集成Quake测绘引擎**: 自动获取Redis服务目标
- **预定义测绘模式**: 
  - 境内Redis未授权访问检测
  - 境内Redis挖矿威胁检测
- **智能数据管理**: 自动去重、本地存储、实时统计
- **无缝扫描集成**: 一键添加测绘目标到扫描列表

#### 2. 多线程扫描引擎
- **真正的多线程并发**: 基于Worker Threads实现
- **智能线程池管理**: 自动创建、分配、回收线程资源
- **性能显著提升**: 2-4倍扫描速度提升
- **Electron兼容**: 通过IPC解决渲染进程限制

### 🔧 技术架构升级

#### 数据库扩展
```sql
-- 新增测绘结果表
CREATE TABLE mapping_results (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  ip TEXT NOT NULL,
  port INTEGER NOT NULL,
  hostname TEXT,
  transport TEXT,
  asn TEXT,
  org TEXT,
  service_name TEXT,
  country_cn TEXT,
  province_cn TEXT,
  city_cn TEXT,
  service_response TEXT,
  service_cert TEXT,
  time TEXT,
  mapping_type TEXT NOT NULL,
  query_used TEXT NOT NULL,
  raw_data TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(ip, port, mapping_type)
);

-- 新增测绘任务表
CREATE TABLE mapping_tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  task_name TEXT NOT NULL,
  mapping_type TEXT NOT NULL,
  query_statement TEXT NOT NULL,
  total_count INTEGER DEFAULT 0,
  fetched_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending',
  pagination_id TEXT,
  start_time TEXT,
  end_time TEXT,
  error_message TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### 新增核心模块
- `src/quake-mapper.js` - Quake API客户端和测绘逻辑
- `src/thread-pool.js` - 线程池管理器
- `src/worker/redis-worker.js` - Worker线程脚本

### 🎨 用户界面改进

#### 新增测绘页面
- **直观的配置界面**: Token验证、模式选择、参数设置
- **实时进度监控**: 进度条、统计信息、用户状态
- **结果管理**: 表格展示、分页浏览、导出功能
- **集成操作**: 一键添加到扫描、批量处理

#### 设置页面扩展
- **多线程配置**: 启用开关、线程数量设置
- **测绘引擎配置**: Quake Token管理
- **性能优化选项**: 智能推荐配置

### 📊 性能提升数据

#### 扫描性能
- **传统扫描**: 单线程异步处理
- **多线程扫描**: 2-4倍速度提升
- **资源利用**: 更好的CPU多核利用率
- **用户体验**: 主线程不阻塞，界面保持响应

#### 测绘效率
- **数据获取**: 支持大批量数据获取（最多10000条）
- **实时处理**: 边获取边存储，防止数据丢失
- **智能去重**: 基于IP+端口的唯一性约束
- **网络优化**: 分批请求，避免超时

### 🛡️ 稳定性增强

#### 错误处理机制
- **自动重试**: 网络异常自动重试
- **优雅降级**: 多线程失败自动回退到传统扫描
- **资源清理**: 及时释放线程和网络资源
- **异常隔离**: 单个任务失败不影响整体运行

#### 数据安全
- **本地存储**: 所有数据本地保存，保护隐私
- **备份机制**: 支持数据导出和备份
- **去重保护**: 避免重复数据占用存储空间

### 📋 使用指南

#### 快速开始
1. **启动应用**: `npm start`
2. **配置Token**: 在设置页面输入Quake API Token
3. **执行测绘**: 选择模式，开始获取目标
4. **批量扫描**: 添加目标到扫描列表，启用多线程扫描

#### 最佳实践
- **合理配置线程数**: 建议设置为CPU核心数
- **控制测绘规模**: 单次查询建议1000-5000条
- **监控积分使用**: 定期检查Quake账户积分
- **定期清理数据**: 及时清理过期的测绘结果

### 🔍 测试验证

#### 功能测试
```bash
# 测试多线程扫描
npm run test-multithread

# 测试测绘模块
npm run test-mapping

# 性能对比测试
node performance-test.js
```

#### 兼容性测试
- **操作系统**: Windows 10/11, macOS, Linux
- **Node.js版本**: 12.0+ (支持Worker Threads)
- **Electron版本**: 28.1.0+

### 📚 文档资源

#### 新增文档
- `MULTITHREAD_README.md` - 多线程扫描详细说明
- `MULTITHREAD_USAGE.md` - 多线程使用指南
- `MAPPING_README.md` - 测绘模块使用指南
- `MAPPING_DEMO.md` - 测绘功能演示
- `TROUBLESHOOTING.md` - 故障排除指南

#### 技术文档
- API规范文档（`./API规范/`目录）
- 数据库设计文档
- 架构设计说明

### 🚨 注意事项

#### 使用限制
- **Quake积分**: 测绘功能需要消耗Quake积分
- **网络要求**: 需要稳定的互联网连接
- **系统资源**: 多线程扫描会增加CPU和内存使用

#### 合规要求
- **授权使用**: 仅用于授权的安全测试
- **法律合规**: 遵守相关法律法规
- **数据保护**: 妥善保护获取的数据

### 🔮 未来规划

#### 计划功能
- **更多测绘引擎**: 集成Shodan、Fofa等
- **自定义查询**: 支持用户自定义测绘语句
- **报告生成**: 自动生成安全评估报告
- **API接口**: 提供RESTful API接口

#### 性能优化
- **缓存机制**: 智能缓存测绘结果
- **增量更新**: 支持增量数据更新
- **分布式扫描**: 支持多机协同扫描

---

## 升级说明

### 从1.x版本升级
1. **备份数据**: 导出现有扫描结果
2. **更新代码**: 拉取最新代码
3. **安装依赖**: `npm install`
4. **数据库升级**: 自动创建新表结构
5. **配置迁移**: 重新配置Quake Token

### 配置检查
- 验证多线程配置是否正确
- 确认Quake Token有效性
- 检查网络连接状态

---

**版本发布时间**: 2024年1月
**兼容性**: 向后兼容1.x版本数据
**支持**: 如有问题请查看故障排除指南或联系技术支持
