const Redis = require('ioredis');
const { saveScanResult, saveBatchScanResults, getRansomwareFeatures } = require('./database');
const axios = require('axios');
const { formatDateForMySQL, buildApiUrl } = require('./utils');
const { Worker } = require('worker_threads');
const path = require('path');
const os = require('os');
const ThreadPool = require('./thread-pool');

// 扫描单个Redis服务
async function scanRedis(host, port) {
  const startTime = new Date();
  // 使用工具函数格式化日期
  const formattedDate = formatDateForMySQL(startTime);

  let result = {
    host,
    port,
    status: 0, // 0: 失败, 1: 成功
    connect_time: formattedDate,
    status_info: '',
    key_values: {},
    is_ransomware: 0,
    auto_detected: 0
  };

  let redis = null;

  try {
    // 创建一个Promise，用于处理连接逻辑
    const resultPromise = await new Promise((resolve) => {
      try {
        // 尝试无密码连接Redis
        redis = new Redis({
          host,
          port,
          connectTimeout: 5000, // 连接超时时间5秒
          retryStrategy: () => null, // 禁用重试
          password: '' // 无密码
        });

        // 监听错误
        redis.on('error', async (err) => {
          result.status = 0;
          result.status_info = `连接失败: ${err.message}`;
          try {
            await saveScanResult(result);
          } catch (saveErr) {
            console.error('保存扫描结果失败:', saveErr.message);
          }

          if (redis) {
            redis.disconnect();
          }

          resolve(result);
        });

        // 连接成功
        redis.on('connect', async () => {
          result.status = 1;
          result.status_info = '连接成功，无需密码';

          try {
            // 获取所有数据库
            const dbCount = await getDbCount(redis);

            // 遍历前5个数据库
            for (let i = 0; i < Math.min(dbCount, 5); i++) {
              await redis.select(i);

              // 获取前10个key
              const keys = await redis.keys('*');
              const limitedKeys = keys.slice(0, 10);

              if (limitedKeys.length > 0) {
                result.key_values[`db${i}`] = {};

                // 获取每个key的值
                for (const key of limitedKeys) {
                  const type = await redis.type(key);
                  let value = null;

                  switch (type) {
                    case 'string':
                      value = await redis.get(key);
                      break;
                    case 'list':
                      value = await redis.lrange(key, 0, 9); // 获取前10个元素
                      break;
                    case 'set':
                      value = await redis.smembers(key);
                      break;
                    case 'hash':
                      value = await redis.hgetall(key);
                      break;
                    case 'zset':
                      value = await redis.zrange(key, 0, 9, 'WITHSCORES'); // 获取前10个元素
                      break;
                    default:
                      value = `不支持的类型: ${type}`;
                  }

                  result.key_values[`db${i}`][key] = {
                    type,
                    value
                  };
                }
              }
            }

            // 自动检测是否被勒索
            try {
              const { isRansomware, matchedFeature, matchType } = await detectRansomware(result.key_values);
              if (isRansomware) {
                result.is_ransomware = 1;
                result.auto_detected = 1;

                // 根据匹配类型显示不同的消息
                let matchTypeDisplay = '';
                if (matchType === 'exact') {
                  matchTypeDisplay = '精确匹配';
                } else if (matchType === 'regex') {
                  matchTypeDisplay = '正则匹配';
                } else if (matchType === 'builtin') {
                  matchTypeDisplay = '内置关键词';
                }

                result.status_info += ` | 自动检测到勒索特征: ${matchedFeature} (${matchTypeDisplay})`;
              }
            } catch (detectErr) {
              console.error('检测勒索失败:', detectErr.message);
              // 继续执行，不中断扫描
            }

          } catch (err) {
            result.status_info += ` | 获取数据失败: ${err.message}`;
          } finally {
            try {
              await saveScanResult(result);
            } catch (saveErr) {
              console.error('保存扫描结果失败:', saveErr.message);
            }

            if (redis) {
              redis.disconnect();
            }

            resolve(result);
          }
        });

        // 添加超时处理
        setTimeout(() => {
          if (result.status === 0 && result.status_info === '') {
            result.status_info = '连接超时';
            if (redis) {
              redis.disconnect();
            }
            resolve(result);
          }
        }, 6000); // 比connectTimeout稍长一点

      } catch (err) {
        result.status = 0;
        result.status_info = `连接失败: ${err.message}`;

        try {
          saveScanResult(result).catch(saveErr => {
            console.error('保存扫描结果失败:', saveErr.message);
          });
        } catch (saveErr) {
          console.error('保存扫描结果失败:', saveErr.message);
        }

        if (redis) {
          redis.disconnect();
        }

        resolve(result);
      }
    });

    return resultPromise;
  } catch (outerErr) {
    console.error('扫描过程中发生未捕获的错误:', outerErr.message);
    result.status = 0;
    result.status_info = `扫描过程中发生错误: ${outerErr.message}`;

    try {
      await saveScanResult(result);
    } catch (saveErr) {
      console.error('保存扫描结果失败:', saveErr.message);
    }

    return result;
  }
}

// 获取Redis数据库数量
async function getDbCount(redis) {
  try {
    const info = await redis.info('keyspace');
    const lines = info.split('\n');
    let maxDb = 0;

    for (const line of lines) {
      if (line.startsWith('db')) {
        const dbNum = parseInt(line.split(':')[0].substring(2));
        maxDb = Math.max(maxDb, dbNum);
      }
    }

    return maxDb + 1; // 数据库从0开始计数
  } catch (err) {
    console.error('获取数据库数量失败:', err.message);
    return 1; // 默认返回1个数据库
  }
}

// 批量扫描Redis服务
async function batchScanRedis(targets) {
  const results = [];
  const concurrency = 10; // 并发数

  // 将targets解析为host和port
  const parsedTargets = targets.map(target => {
    const [host, port] = target.split(':');
    return { host, port: parseInt(port) || 6379 }; // 默认端口6379
  });

  // 分批处理
  for (let i = 0; i < parsedTargets.length; i += concurrency) {
    const batch = parsedTargets.slice(i, i + concurrency);
    const promises = batch.map(({ host, port }) => scanRedis(host, port));

    const batchResults = await Promise.all(promises);
    results.push(...batchResults);
  }

  // 执行去重操作
  await saveBatchScanResults(results);

  return results;
}

// 检测是否被勒索
async function detectRansomware(keyValues) {
  try {
    // 获取勒索特征
    const features = await getRansomwareFeatures();

    // 转换keyValues为字符串进行匹配
    const keyValuesStr = JSON.stringify(keyValues).toLowerCase();

    for (const feature of features) {
      const featureText = feature.feature_text;
      const matchType = feature.match_type || 'exact'; // 默认为精确匹配

      if (matchType === 'exact') {
        // 精确匹配（不区分大小写）
        if (keyValuesStr.toLowerCase().includes(featureText.toLowerCase())) {
          return { isRansomware: true, matchedFeature: featureText, matchType: 'exact' };
        }
      } else if (matchType === 'regex') {
        // 正则表达式匹配
        try {
          const regex = new RegExp(featureText, 'i'); // 'i'标志表示不区分大小写
          if (regex.test(keyValuesStr)) {
            return { isRansomware: true, matchedFeature: featureText, matchType: 'regex' };
          }
        } catch (e) {
          console.error('正则表达式错误:', e.message);
          // 继续检查下一个特征
          continue;
        }
      }
    }

    // 常见挖矿特征关键词
    const commonRansomwareKeywords = [
      'miner', 'mining', 'cpu', 'xmr', 'monero', 'bitcoin', 'btc',
      'crypto', 'pool', 'hashrate', 'wallet', 'stratum', 'coinhive'
    ];

    for (const keyword of commonRansomwareKeywords) {
      if (keyValuesStr.includes(keyword)) {
        return { isRansomware: true, matchedFeature: keyword, matchType: 'builtin' };
      }
    }

    return { isRansomware: false, matchedFeature: null, matchType: null };
  } catch (err) {
    console.error('检测挖矿失败:', err.message);
    return { isRansomware: false, matchedFeature: null, matchType: null };
  }
}

// 上传扫描结果到远程服务器
async function uploadResults(apiUrl, results, apiKey, clientId, clientName) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const fullApiUrl = buildApiUrl(apiUrl, 'scan/results');
    console.log('上传URL:', fullApiUrl);

    // 处理日期时间格式
    const formattedResults = results.map(result => {
      // 创建结果的深拷贝，避免修改原始对象
      const formattedResult = { ...result };

      // 使用工具函数格式化所有日期字段
      if (formattedResult.connect_time) {
        formattedResult.connect_time = formatDateForMySQL(formattedResult.connect_time);
      }

      if (formattedResult.created_at) {
        formattedResult.created_at = formatDateForMySQL(formattedResult.created_at);
      }

      if (formattedResult.uploaded_at) {
        formattedResult.uploaded_at = formatDateForMySQL(formattedResult.uploaded_at);
      }

      return formattedResult;
    });

    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey
    };

    // 添加可选的客户端信息
    if (clientId) {
      headers['X-Client-ID'] = clientId;
    }

    if (clientName) {
      headers['X-Client-Name'] = clientName;
    }

    const response = await axios.post(fullApiUrl, { results: formattedResults }, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: '上传成功',
        uploaded: results.length
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '上传失败: 服务器返回未知错误'
      };
    }
  } catch (err) {
    console.error('上传结果出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return { success: false, message: `上传失败: ${errorMessage}` };
  }
}

// 上传单个扫描结果到远程服务器
async function uploadSingleResult(apiUrl, result, apiKey, clientId, clientName) {
  // 将单个结果包装成数组调用上传批量结果的函数
  return await uploadResults(apiUrl, [result], apiKey, clientId, clientName);
}

// 从远程服务器获取挖矿特征
async function fetchFeatures(apiUrl, apiKey) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const featuresUrl = buildApiUrl(apiUrl, 'features');
    console.log('获取特征URL:', featuresUrl);

    const headers = {
      'X-API-Key': apiKey
    };

    const response = await axios.get(featuresUrl, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        features: response.data.features || []
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '获取特征失败: 服务器返回未知错误',
        features: []
      };
    }
  } catch (err) {
    console.error('获取特征出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `获取特征失败: ${errorMessage}`,
      features: []
    };
  }
}

// 上传新特征到远程服务器
async function uploadFeature(apiUrl, apiKey, feature) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const featuresUrl = buildApiUrl(apiUrl, 'features');
    console.log('上传特征URL:', featuresUrl);

    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey
    };

    const response = await axios.post(featuresUrl, feature, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: '特征上传成功',
        feature: response.data.feature || feature
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '上传特征失败: 服务器返回未知错误'
      };
    }
  } catch (err) {
    console.error('上传特征出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `上传特征失败: ${errorMessage}`
    };
  }
}

// 编辑远程服务器上的特征
async function updateFeature(apiUrl, apiKey, featureId, updatedFeature) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const featuresUrl = buildApiUrl(apiUrl, 'features');
    // 添加特征ID
    const featureUrl = `${featuresUrl}/${featureId}`;
    console.log('更新特征URL:', featureUrl);

    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey
    };

    const response = await axios.put(featureUrl, updatedFeature, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: '特征更新成功'
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '更新特征失败: 服务器返回未知错误'
      };
    }
  } catch (err) {
    console.error('更新特征出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `更新特征失败: ${errorMessage}`
    };
  }
}

// 删除远程服务器上的特征
async function deleteRemoteFeature(apiUrl, apiKey, featureId) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const featuresUrl = buildApiUrl(apiUrl, 'features');
    // 添加特征ID
    const featureUrl = `${featuresUrl}/${featureId}`;
    console.log('删除特征URL:', featureUrl);

    const headers = {
      'X-API-Key': apiKey
    };

    const response = await axios.delete(featureUrl, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: '特征删除成功'
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '删除特征失败: 服务器返回未知错误'
      };
    }
  } catch (err) {
    console.error('删除特征出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `删除特征失败: ${errorMessage}`
    };
  }
}

// 从远程服务器获取扫描结果
async function fetchScanResults(apiUrl, apiKey, limit = 100, offset = 0) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    let resultsUrl = buildApiUrl(apiUrl, 'scan/results');

    // 添加查询参数
    resultsUrl += `?limit=${limit}&offset=${offset}`;

    console.log('获取扫描结果URL:', resultsUrl);

    const headers = {
      'X-API-Key': apiKey
    };

    const response = await axios.get(resultsUrl, { headers });

    if (response.data && response.data.success) {
      // 确保results是数组
      const results = Array.isArray(response.data.results) ? response.data.results : [];

      // 处理每个结果对象的key_values字段
      const processedResults = results.map(result => {
        // 创建结果的深拷贝，避免修改原始对象
        const processedResult = { ...result };

        // 处理key_values字段
        if (processedResult.key_values) {
          // 如果key_values是字符串，尝试解析成对象
          if (typeof processedResult.key_values === 'string') {
            try {
              processedResult.key_values = JSON.parse(processedResult.key_values);
            } catch (e) {
              console.warn(`无法解析结果ID ${processedResult.id} 的key_values:`, e.message);
              // 如果解析失败，设置为空对象
              processedResult.key_values = {};
            }
          }
          // 如果key_values不是对象或数组，也设置为空对象
          else if (typeof processedResult.key_values !== 'object') {
            console.warn(`结果ID ${processedResult.id} 的key_values不是对象类型:`, typeof processedResult.key_values);
            processedResult.key_values = {};
          }
        } else {
          // 如果key_values不存在，设置为空对象
          processedResult.key_values = {};
        }

        return processedResult;
      });

      return {
        success: true,
        results: processedResults,
        count: response.data.count || processedResults.length
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '获取扫描结果失败: 服务器返回未知错误',
        results: [],
        count: 0
      };
    }
  } catch (err) {
    console.error('获取扫描结果出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `获取扫描结果失败: ${errorMessage}`,
      results: [],
      count: 0
    };
  }
}

// 获取远程扫描结果统计信息
async function fetchScanStats(apiUrl, apiKey) {
  if (!apiUrl) return { success: false, message: '未配置API服务器地址' };
  if (!apiKey) return { success: false, message: '未配置API密钥' };

  try {
    // 构建API URL
    const statsUrl = buildApiUrl(apiUrl, 'scan/stats');

    console.log('获取扫描统计URL:', statsUrl);

    const headers = {
      'X-API-Key': apiKey
    };

    const response = await axios.get(statsUrl, { headers });

    if (response.data && response.data.success) {
      return {
        success: true,
        stats: response.data.stats || {}
      };
    } else {
      return {
        success: false,
        message: response.data?.message || '获取扫描统计失败: 服务器返回未知错误',
        stats: {}
      };
    }
  } catch (err) {
    console.error('获取扫描统计出错:', err.message);
    // 提取更详细的错误信息
    const errorMessage = err.response?.data?.message || err.message;
    return {
      success: false,
      message: `获取扫描统计失败: ${errorMessage}`,
      stats: {}
    };
  }
}

// 多线程批量扫描Redis服务
async function batchScanRedisMultiThread(targets, options = {}) {
  const {
    maxThreads = Math.min(os.cpus().length, 8), // 默认线程数
    onProgress = null, // 进度回调函数
    onScanComplete = null // 单个扫描完成回调
  } = options;

  console.log(`开始多线程批量扫描，目标数量: ${targets.length}，线程数: ${maxThreads}`);

  // 创建线程池
  const threadPool = new ThreadPool({
    maxWorkers: maxThreads,
    workerScript: path.join(__dirname, 'worker', 'redis-worker.js')
  });

  try {
    // 初始化线程池
    await threadPool.initialize();

    // 获取勒索特征数据，传递给Worker线程
    const ransomwareFeatures = await getRansomwareFeatures();

    // 解析目标
    const parsedTargets = targets.map(target => {
      const [host, port] = target.split(':');
      return { host, port: parseInt(port) || 6379 };
    });

    // 创建一个映射，用于跟踪每个host:port的最新扫描结果
    const latestResults = new Map();
    const results = [];
    let completedCount = 0;

    // 创建所有扫描任务的Promise
    const scanPromises = parsedTargets.map(async ({ host, port }) => {
      try {
        const result = await threadPool.execute({
          host,
          port,
          ransomwareFeatures
        });

        // 在本地进行去重，使用host:port作为键
        const key = `${result.host}:${result.port}`;
        latestResults.set(key, result);

        completedCount++;

        // 调用进度回调
        if (onProgress) {
          onProgress(completedCount, parsedTargets.length);
        }

        // 调用单个扫描完成回调
        if (onScanComplete) {
          onScanComplete(result);
        }

        return result;
      } catch (error) {
        console.error(`扫描 ${host}:${port} 失败:`, error.message);

        // 创建失败结果
        const failResult = {
          host,
          port,
          status: 0,
          connect_time: formatDateForMySQL(new Date()),
          status_info: `扫描失败: ${error.message}`,
          key_values: {},
          is_ransomware: 0,
          auto_detected: 0
        };

        completedCount++;

        // 调用进度回调
        if (onProgress) {
          onProgress(completedCount, parsedTargets.length);
        }

        // 调用单个扫描完成回调
        if (onScanComplete) {
          onScanComplete(failResult);
        }

        return failResult;
      }
    });

    // 等待所有扫描完成
    const allResults = await Promise.all(scanPromises);

    // 使用去重后的结果
    const uniqueResults = Array.from(latestResults.values());

    console.log(`多线程扫描完成，共扫描 ${allResults.length} 个目标，去重后 ${uniqueResults.length} 个结果`);

    // 注意：扫描结果现在是通过onScanComplete回调实时保存的，不需要在这里批量保存
    console.log('多线程扫描结果已通过实时回调保存到数据库');

    return uniqueResults;

  } finally {
    // 关闭线程池
    await threadPool.shutdown();
  }
}

// 带进度回调的多线程批量扫描函数
async function batchScanRedisMultiThreadWithProgress(targets, onScanComplete, options = {}) {
  return await batchScanRedisMultiThread(targets, {
    ...options,
    onScanComplete
  });
}

module.exports = {
  scanRedis,
  batchScanRedis,
  batchScanRedisMultiThread,
  batchScanRedisMultiThreadWithProgress,
  uploadResults,
  uploadSingleResult,
  fetchFeatures,
  uploadFeature,
  updateFeature,
  deleteRemoteFeature,
  fetchScanResults,
  fetchScanStats
};