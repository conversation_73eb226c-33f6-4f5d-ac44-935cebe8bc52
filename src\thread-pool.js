const { Worker } = require('worker_threads');
const path = require('path');
const os = require('os');

class ThreadPool {
  constructor(options = {}) {
    this.maxWorkers = options.maxWorkers || Math.min(os.cpus().length, 8); // 默认使用CPU核心数，最多8个
    this.workerScript = options.workerScript || path.join(__dirname, 'worker', 'redis-worker.js');
    this.workers = [];
    this.availableWorkers = [];
    this.taskQueue = [];
    this.taskId = 0;
    this.pendingTasks = new Map();
    this.isShuttingDown = false;
  }

  // 初始化线程池
  async initialize() {
    console.log(`初始化线程池，创建 ${this.maxWorkers} 个Worker线程`);
    
    for (let i = 0; i < this.maxWorkers; i++) {
      await this.createWorker();
    }
    
    console.log(`线程池初始化完成，共 ${this.workers.length} 个Worker线程`);
  }

  // 创建一个Worker线程
  async createWorker() {
    return new Promise((resolve, reject) => {
      try {
        const worker = new Worker(this.workerScript);
        
        worker.on('message', (data) => {
          this.handleWorkerMessage(worker, data);
        });
        
        worker.on('error', (error) => {
          console.error('Worker线程错误:', error);
          this.handleWorkerError(worker, error);
        });
        
        worker.on('exit', (code) => {
          if (code !== 0) {
            console.error(`Worker线程异常退出，退出码: ${code}`);
          }
          this.handleWorkerExit(worker);
        });
        
        // 添加Worker到池中
        this.workers.push(worker);
        this.availableWorkers.push(worker);
        
        resolve(worker);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 处理Worker消息
  handleWorkerMessage(worker, data) {
    const { taskId, success, result, error } = data;
    
    // 获取待处理的任务
    const pendingTask = this.pendingTasks.get(taskId);
    if (!pendingTask) {
      console.warn(`收到未知任务ID的响应: ${taskId}`);
      return;
    }
    
    // 移除待处理任务
    this.pendingTasks.delete(taskId);
    
    // 将Worker标记为可用
    this.availableWorkers.push(worker);
    
    // 处理任务结果
    if (success) {
      pendingTask.resolve(result);
    } else {
      pendingTask.reject(new Error(error));
    }
    
    // 处理队列中的下一个任务
    this.processNextTask();
  }

  // 处理Worker错误
  handleWorkerError(worker, error) {
    // 找到使用此Worker的任务并拒绝它们
    for (const [taskId, pendingTask] of this.pendingTasks.entries()) {
      if (pendingTask.worker === worker) {
        this.pendingTasks.delete(taskId);
        pendingTask.reject(error);
      }
    }
    
    // 从可用Worker列表中移除
    const availableIndex = this.availableWorkers.indexOf(worker);
    if (availableIndex !== -1) {
      this.availableWorkers.splice(availableIndex, 1);
    }
    
    // 从Worker列表中移除
    const workerIndex = this.workers.indexOf(worker);
    if (workerIndex !== -1) {
      this.workers.splice(workerIndex, 1);
    }
    
    // 如果不在关闭状态，创建新的Worker替换
    if (!this.isShuttingDown) {
      this.createWorker().catch(err => {
        console.error('创建替换Worker失败:', err);
      });
    }
  }

  // 处理Worker退出
  handleWorkerExit(worker) {
    // 从可用Worker列表中移除
    const availableIndex = this.availableWorkers.indexOf(worker);
    if (availableIndex !== -1) {
      this.availableWorkers.splice(availableIndex, 1);
    }
    
    // 从Worker列表中移除
    const workerIndex = this.workers.indexOf(worker);
    if (workerIndex !== -1) {
      this.workers.splice(workerIndex, 1);
    }
  }

  // 执行任务
  async execute(taskData) {
    return new Promise((resolve, reject) => {
      const currentTaskId = ++this.taskId;
      
      const task = {
        id: currentTaskId,
        data: { ...taskData, taskId: currentTaskId },
        resolve,
        reject,
        worker: null
      };
      
      // 如果有可用的Worker，立即执行
      if (this.availableWorkers.length > 0) {
        this.assignTaskToWorker(task);
      } else {
        // 否则加入队列
        this.taskQueue.push(task);
      }
    });
  }

  // 将任务分配给Worker
  assignTaskToWorker(task) {
    const worker = this.availableWorkers.pop();
    if (!worker) {
      // 没有可用Worker，加入队列
      this.taskQueue.push(task);
      return;
    }
    
    task.worker = worker;
    this.pendingTasks.set(task.id, task);
    
    // 发送任务到Worker
    worker.postMessage(task.data);
  }

  // 处理队列中的下一个任务
  processNextTask() {
    if (this.taskQueue.length > 0 && this.availableWorkers.length > 0) {
      const nextTask = this.taskQueue.shift();
      this.assignTaskToWorker(nextTask);
    }
  }

  // 获取线程池状态
  getStatus() {
    return {
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      busyWorkers: this.workers.length - this.availableWorkers.length,
      queuedTasks: this.taskQueue.length,
      pendingTasks: this.pendingTasks.size
    };
  }

  // 等待所有任务完成
  async waitForAllTasks() {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        if (this.taskQueue.length === 0 && this.pendingTasks.size === 0) {
          resolve();
        } else {
          setTimeout(checkCompletion, 100);
        }
      };
      
      checkCompletion();
    });
  }

  // 关闭线程池
  async shutdown() {
    console.log('开始关闭线程池...');
    this.isShuttingDown = true;
    
    // 等待所有任务完成
    await this.waitForAllTasks();
    
    // 终止所有Worker
    const terminationPromises = this.workers.map(worker => {
      return worker.terminate();
    });
    
    await Promise.all(terminationPromises);
    
    // 清理
    this.workers = [];
    this.availableWorkers = [];
    this.taskQueue = [];
    this.pendingTasks.clear();
    
    console.log('线程池已关闭');
  }
}

module.exports = ThreadPool;
