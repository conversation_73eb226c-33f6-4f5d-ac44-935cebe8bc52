// 将日期格式化为MySQL兼容格式的工具函数
function formatDateForMySQL(date) {
  if (!date) return null;
  
  if (typeof date === 'string') {
    // 如果是ISO格式的字符串
    if (date.includes('T')) {
      try {
        return date.slice(0, 19).replace('T', ' ');
      } catch (e) {
        console.warn('格式化日期时间失败:', e.message);
        return date;
      }
    }
    return date;
  } else if (date instanceof Date) {
    // 如果是Date对象
    try {
      return date.toISOString().slice(0, 19).replace('T', ' ');
    } catch (e) {
      console.warn('格式化日期时间失败:', e.message);
      return date.toString();
    }
  }
  
  return String(date);
}

// 构建API URL的工具函数
function buildApiUrl(baseUrl, endpoint) {
  // 移除baseUrl末尾的斜杠
  let url = baseUrl.replace(/\/+$/, '');
  
  // 检查URL是否已经包含/api前缀
  if (!url.includes('/api')) {
    url += '/api';
  }
  
  // 如果URL已经包含完整路径但不是目标路径，需要修改
  if (url.includes('/api/scan/results') && !endpoint.includes('scan/results')) {
    url = url.replace('/api/scan/results', '/api');
  } else if (url.includes('/api/features') && !endpoint.includes('features')) {
    url = url.replace('/api/features', '/api');
  } else if (url.includes('/api/scan') && !endpoint.includes('scan')) {
    url = url.replace('/api/scan', '/api');
  }
  
  // 如果URL以/api结尾，直接添加endpoint
  if (url.endsWith('/api')) {
    url += `/${endpoint}`;
  } else {
    // 检查是否需要添加/api前缀
    if (!url.endsWith('/api/' + endpoint)) {
      if (url.endsWith('/api')) {
        url += `/${endpoint}`;
      } else {
        url += `/api/${endpoint}`;
      }
    }
  }
  
  return url;
}

module.exports = {
  formatDateForMySQL,
  buildApiUrl
}; 