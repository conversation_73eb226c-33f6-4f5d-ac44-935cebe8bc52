const { batchScanRedis, batchScanRedisMultiThread } = require('./src/redis-scanner');
const { initDb } = require('./src/database');

async function performanceTest() {
  console.log('Redis扫描性能对比测试');
  console.log('='.repeat(50));
  
  try {
    // 初始化数据库
    await initDb();
    
    // 测试目标列表（使用一些不存在的IP来模拟真实扫描环境）
    const targets = [
      '*************:6379',
      '*************:6379',
      '*************:6379',
      '*************:6379',
      '*************:6379',
      '**********:6379',
      '**********:6379',
      '**********:6379',
      '************:6379',
      '************:6379'
    ];
    
    console.log(`测试目标数量: ${targets.length}`);
    console.log(`目标列表: ${targets.slice(0, 3).join(', ')}...`);
    console.log('');
    
    // 测试1: 传统异步并发扫描
    console.log('测试1: 传统异步并发扫描');
    console.log('-'.repeat(30));
    
    const start1 = Date.now();
    const results1 = await batchScanRedis(targets);
    const end1 = Date.now();
    const time1 = end1 - start1;
    
    console.log(`耗时: ${time1}ms`);
    console.log(`结果数: ${results1.length}`);
    console.log(`成功: ${results1.filter(r => r.status === 1).length}`);
    console.log(`失败: ${results1.filter(r => r.status === 0).length}`);
    console.log('');
    
    // 等待一段时间，避免连续测试的影响
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试2: 多线程扫描
    console.log('测试2: 多线程扫描 (4线程)');
    console.log('-'.repeat(30));
    
    const start2 = Date.now();
    const results2 = await batchScanRedisMultiThread(targets, {
      maxThreads: 4,
      onProgress: (completed, total) => {
        process.stdout.write(`\r进度: ${completed}/${total}`);
      }
    });
    const end2 = Date.now();
    const time2 = end2 - start2;
    
    console.log(`\n耗时: ${time2}ms`);
    console.log(`结果数: ${results2.length}`);
    console.log(`成功: ${results2.filter(r => r.status === 1).length}`);
    console.log(`失败: ${results2.filter(r => r.status === 0).length}`);
    console.log('');
    
    // 性能对比
    console.log('性能对比结果');
    console.log('='.repeat(30));
    console.log(`传统扫描耗时: ${time1}ms`);
    console.log(`多线程扫描耗时: ${time2}ms`);
    
    if (time2 < time1) {
      const improvement = ((time1 - time2) / time1 * 100).toFixed(1);
      console.log(`性能提升: ${improvement}% (快了 ${time1 - time2}ms)`);
    } else {
      const degradation = ((time2 - time1) / time1 * 100).toFixed(1);
      console.log(`性能下降: ${degradation}% (慢了 ${time2 - time1}ms)`);
    }
    
    console.log('');
    console.log('注意: 性能提升取决于网络条件、CPU核心数和目标响应时间');
    console.log('在实际网络扫描中，多线程的优势会更加明显');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  performanceTest().then(() => {
    console.log('\n测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { performanceTest };
