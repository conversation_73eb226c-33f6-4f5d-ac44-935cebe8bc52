服务数据实时查询接口
接口地址：/api/v3/search/quake_service
请求方式：POST
该接口仅适用小批量的数据查询，若存在深度翻页查询需求（即一次性查询10W数据），请移步下面的深度查询接口。
接口参数
字段	类型	必填	默认值	备注
query	str	是	*	查询语句
rule	str	否	无	类型为IP列表的服务数据收藏名称
ip_list	List[str]	否	无	IP列表
start	int	否	0	返回结果的下标切片位置
size	int	否	10	返回结果的切片长度
ignore_cache	bool	否	false	是否忽略缓存
start_time	str	否	无	查询起始时间，接受2020-10-14 00:00:00格式的数据，时区为UTC
end_time	str	否	无	查询截止时间，接受2020-10-14 00:00:00格式的数据，时区为UTC
include	List(str)	否	无	包含字段
exclude	List(str)	否	无	排除字段
latest	bool	否	false	是否使用最新数据
shortcuts	list(str)	否	无	对应web页面里的 过滤无效请求 排除蜜罐 排除CDN （最新值请从WEB的url里获取）
仅付费用户能够指定查询时间，非付费用户默认仅查询近一年的数据
include 和 exclude参数，可传参字段从获取可筛选服务字段接口获取
注册用户：

服务数据：'ip', 'port', 'hostname', 'transport', 'asn', 'org', 'service.name', 'location.country_cn', 'location.province_cn', 'location.city_cn', 'service.http.host', 'service.http.title', 'service.http.server'
主机数据：ip、asn，org，location.country_cn，location.province_cn，location.city_cn
会员用户：

服务数据：'ip', 'port', 'hostname', 'transport', 'asn', 'org', 'service.name', 'location.country_cn', 'location.province_cn', 'location.city_cn', 'service.http.host', 'time', 'service.http.title', 'service.http.server', 'service.response', 'service.cert', 'components.product_catalog', 'components.product_type', 'components.product_level', 'components.product_vendor', 'location.country_en', 'location.province_en', 'location.city_en', 'location.district_en', 'location.district_cn', 'location.isp', 'service.http.body', 'components.product_name_cn', 'components.version', 'service.http.infomation.mail', 'service.http.favicon.hash', 'service.http.favicon.data', 'domain', 'service.http.status_code'
主机数据：ip、asn，org，location.country_cn，location.province_cn，location.city_cn，hostname，time，location.country_en，location.province_en，location.city_en，location.street_en，location.street_cn，location.owner，location.gps
使用范例
shell请求范例
curl --location 'https://quake.360.net/api/v3/search/quake_service' \
--header 'X-QuakeToken: xxxxxx' \
--header 'Content-Type: application/json' \
--data '{
    "query": " country:\"china\"",
    "start": 0,
    "size": 10,
    "ignore_cache": "False",
    "latest": "True",
    "shortcuts": [
        "610ce2adb1a2e3e1632e67b1"
    ]
}'
python请求范例
import requests

headers = {
    "X-QuakeToken": "d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx",
    "Content-Type": "application/json"
}

data = {
    "query": """service: http""",
    "start": 0,
    "size": 1,
    "ignore_cache": False,
    "latest": True,
    "shortcuts": ["610ce2adb1a2e3e1632e67b1"],
}

response = requests.post(
    url="https://quake.360.net/api/v3/search/quake_service", headers=headers, json=data
)
print(response.json())
返回值范例
{
  "code": 0,
  "message": "Successful.",
  "data": [
      // 返回数据
  ],
  "meta": {
    "pagination": {
      "count": 1,
      "page_index": 1,
      "page_size": 1,
      "total": 68184
    }
  }
}