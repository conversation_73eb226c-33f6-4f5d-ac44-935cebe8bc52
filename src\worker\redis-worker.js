const { parentPort, workerData } = require('worker_threads');
const Redis = require('ioredis');

// 扫描单个Redis服务的Worker版本
async function scanRedisWorker(host, port, ransomwareFeatures) {
  const startTime = new Date();
  const formattedDate = formatDateForMySQL(startTime);

  let result = {
    host,
    port,
    status: 0, // 0: 失败, 1: 成功
    connect_time: formattedDate,
    status_info: '',
    key_values: {},
    is_ransomware: 0,
    auto_detected: 0
  };

  let redis = null;

  try {
    // 创建一个Promise，用于处理连接逻辑
    const resultPromise = await new Promise((resolve) => {
      try {
        // 尝试无密码连接Redis
        redis = new Redis({
          host,
          port,
          connectTimeout: 5000, // 连接超时时间5秒
          retryStrategy: () => null, // 禁用重试
          password: '' // 无密码
        });

        // 监听错误
        redis.on('error', async (err) => {
          result.status = 0;
          result.status_info = `连接失败: ${err.message}`;

          if (redis) {
            redis.disconnect();
          }

          resolve(result);
        });

        // 连接成功
        redis.on('connect', async () => {
          result.status = 1;
          result.status_info = '连接成功，无需密码';

          try {
            // 获取所有数据库
            const dbCount = await getDbCount(redis);

            // 遍历前5个数据库
            for (let i = 0; i < Math.min(dbCount, 5); i++) {
              await redis.select(i);

              // 获取前10个key
              const keys = await redis.keys('*');
              const limitedKeys = keys.slice(0, 10);

              if (limitedKeys.length > 0) {
                result.key_values[`db${i}`] = {};

                // 获取每个key的值
                for (const key of limitedKeys) {
                  const type = await redis.type(key);
                  let value = null;

                  switch (type) {
                    case 'string':
                      value = await redis.get(key);
                      break;
                    case 'list':
                      value = await redis.lrange(key, 0, 9); // 获取前10个元素
                      break;
                    case 'set':
                      value = await redis.smembers(key);
                      break;
                    case 'hash':
                      value = await redis.hgetall(key);
                      break;
                    case 'zset':
                      value = await redis.zrange(key, 0, 9, 'WITHSCORES'); // 获取前10个元素
                      break;
                    default:
                      value = `不支持的类型: ${type}`;
                  }

                  result.key_values[`db${i}`][key] = {
                    type,
                    value
                  };
                }
              }
            }

            // 自动检测是否被勒索
            try {
              const { isRansomware, matchedFeature, matchType } = await detectRansomware(result.key_values, ransomwareFeatures);
              if (isRansomware) {
                result.is_ransomware = 1;
                result.auto_detected = 1;

                // 根据匹配类型显示不同的消息
                let matchTypeDisplay = '';
                if (matchType === 'exact') {
                  matchTypeDisplay = '精确匹配';
                } else if (matchType === 'regex') {
                  matchTypeDisplay = '正则匹配';
                } else if (matchType === 'builtin') {
                  matchTypeDisplay = '内置关键词';
                }

                result.status_info += ` | 自动检测到勒索特征: ${matchedFeature} (${matchTypeDisplay})`;
              }
            } catch (detectErr) {
              console.error('检测勒索失败:', detectErr.message);
              // 继续执行，不中断扫描
            }

          } catch (err) {
            result.status_info += ` | 获取数据失败: ${err.message}`;
          } finally {
            if (redis) {
              redis.disconnect();
            }

            resolve(result);
          }
        });

        // 添加超时处理
        setTimeout(() => {
          if (result.status === 0 && result.status_info === '') {
            result.status_info = '连接超时';
            if (redis) {
              redis.disconnect();
            }
            resolve(result);
          }
        }, 6000); // 比connectTimeout稍长一点

      } catch (err) {
        result.status = 0;
        result.status_info = `连接失败: ${err.message}`;

        if (redis) {
          redis.disconnect();
        }

        resolve(result);
      }
    });

    return resultPromise;
  } catch (outerErr) {
    console.error('扫描过程中发生未捕获的错误:', outerErr.message);
    result.status = 0;
    result.status_info = `扫描过程中发生错误: ${outerErr.message}`;

    return result;
  }
}

// 获取Redis数据库数量
async function getDbCount(redis) {
  try {
    // 尝试不同的方式获取数据库信息
    let info;
    try {
      // 首先尝试带参数的info命令
      info = await redis.info('keyspace');
    } catch (err) {
      // 如果失败，尝试不带参数的info命令
      console.log('info keyspace失败，尝试使用info命令:', err.message);
      const fullInfo = await redis.info();
      // 从完整信息中提取keyspace部分
      const lines = fullInfo.split('\n');
      const keyspaceLines = lines.filter(line => line.startsWith('db') || line.includes('keyspace'));
      info = keyspaceLines.join('\n');
    }

    const lines = info.split('\n');
    let maxDb = 0;

    for (const line of lines) {
      if (line.startsWith('db')) {
        const dbNum = parseInt(line.split(':')[0].substring(2));
        if (!isNaN(dbNum)) {
          maxDb = Math.max(maxDb, dbNum);
        }
      }
    }

    return maxDb + 1; // 数据库从0开始计数
  } catch (err) {
    console.error('获取数据库数量失败:', err.message);
    return 1; // 默认返回1个数据库
  }
}

// 检测是否被勒索 (Worker版本)
async function detectRansomware(keyValues, features) {
  try {
    // 转换keyValues为字符串进行匹配
    const keyValuesStr = JSON.stringify(keyValues).toLowerCase();

    for (const feature of features) {
      const featureText = feature.feature_text;
      const matchType = feature.match_type || 'exact'; // 默认为精确匹配

      if (matchType === 'exact') {
        // 精确匹配（不区分大小写）
        if (keyValuesStr.toLowerCase().includes(featureText.toLowerCase())) {
          return { isRansomware: true, matchedFeature: featureText, matchType: 'exact' };
        }
      } else if (matchType === 'regex') {
        // 正则表达式匹配
        try {
          const regex = new RegExp(featureText, 'i'); // 'i'标志表示不区分大小写
          if (regex.test(keyValuesStr)) {
            return { isRansomware: true, matchedFeature: featureText, matchType: 'regex' };
          }
        } catch (e) {
          console.error('正则表达式错误:', e.message);
          // 继续检查下一个特征
          continue;
        }
      }
    }

    // 常见挖矿特征关键词
    const commonRansomwareKeywords = [
      'miner', 'mining', 'cpu', 'xmr', 'monero', 'bitcoin', 'btc',
      'crypto', 'pool', 'hashrate', 'wallet', 'stratum', 'coinhive'
    ];

    for (const keyword of commonRansomwareKeywords) {
      if (keyValuesStr.includes(keyword)) {
        return { isRansomware: true, matchedFeature: keyword, matchType: 'builtin' };
      }
    }

    return { isRansomware: false, matchedFeature: null, matchType: null };
  } catch (err) {
    console.error('检测挖矿失败:', err.message);
    return { isRansomware: false, matchedFeature: null, matchType: null };
  }
}

// 格式化日期为MySQL格式
function formatDateForMySQL(date) {
  if (!date) return null;

  // 如果已经是字符串，直接返回
  if (typeof date === 'string') {
    return date;
  }

  // 如果是Date对象，格式化为MySQL datetime格式
  if (date instanceof Date) {
    return date.toISOString().slice(0, 19).replace('T', ' ');
  }

  // 其他情况，尝试转换为Date对象再格式化
  try {
    const dateObj = new Date(date);
    return dateObj.toISOString().slice(0, 19).replace('T', ' ');
  } catch (err) {
    console.error('日期格式化失败:', err.message);
    return null;
  }
}

// 监听来自主线程的消息
parentPort.on('message', async (data) => {
  const { taskId, host, port, ransomwareFeatures } = data;

  try {
    const result = await scanRedisWorker(host, port, ransomwareFeatures);

    // 发送结果回主线程
    parentPort.postMessage({
      taskId,
      success: true,
      result
    });
  } catch (error) {
    // 发送错误回主线程
    parentPort.postMessage({
      taskId,
      success: false,
      error: error.message
    });
  }
});
