const axios = require('axios');
const { saveBatchMappingResults, saveMappingTask, updateMappingTask, getMappingTasks, getMappingStats } = require('./database');
const { formatDateForMySQL } = require('./utils');

// Quake API 基础配置
const QUAKE_BASE_URL = 'https://quake.360.net/api/v3';

// 预定义的测绘模式
const MAPPING_MODES = {
  'redis_mining': {
    name: '境内Redis挖矿',
    query: 'protocol:"redis" AND response:"db0:keys=4," AND response:"redis_version" AND country:"中国" AND (NOT province:"台湾" AND NOT province:"香港" AND NOT province:"澳门")',
    description: '检测境内可能被用于挖矿的Redis服务'
  },
  'redis_unauthorized': {
    name: '境内Redis未授权',
    query: 'protocol:"redis" AND response:"redis_version" AND country:"中国" AND (NOT province:"台湾" AND NOT province:"香港" AND NOT province:"澳门")',
    description: '检测境内Redis未授权访问服务'
  }
};

// 获取用户信息和剩余积分
async function getUserInfo(token) {
  try {
    const response = await axios.get(`${QUAKE_BASE_URL}/user/info`, {
      headers: {
        'X-QuakeToken': token
      },
      timeout: 10000
    });

    if (response.data.code === 0) {
      return {
        success: true,
        data: response.data.data
      };
    } else {
      return {
        success: false,
        error: response.data.message || '获取用户信息失败'
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error.message);
    return {
      success: false,
      error: `网络请求失败: ${error.message}`
    };
  }
}

// 实时查询接口（适用于小批量数据）
async function searchQuakeService(token, query, options = {}) {
  try {
    const {
      start = 0,
      size = 100,
      latest = true,
      ignore_cache = false,
      start_time,
      end_time
    } = options;

    const requestData = {
      query,
      start,
      size,
      latest,
      ignore_cache
    };

    // 添加时间范围参数
    if (start_time) {
      requestData.start_time = start_time;
    }
    if (end_time) {
      requestData.end_time = end_time;
    }

    const response = await axios.post(`${QUAKE_BASE_URL}/search/quake_service`, requestData, {
      headers: {
        'X-QuakeToken': token,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (response.data.code === 0) {
      return {
        success: true,
        data: response.data.data,
        meta: response.data.meta
      };
    } else {
      return {
        success: false,
        error: response.data.message || '查询失败'
      };
    }
  } catch (error) {
    console.error('Quake查询失败:', error.message);
    return {
      success: false,
      error: `网络请求失败: ${error.message}`
    };
  }
}

// 深度查询接口（适用于大批量数据）
async function scrollQuakeService(token, query, options = {}) {
  try {
    const {
      pagination_id,
      size = 100,
      latest = true,
      ignore_cache = false,
      start_time,
      end_time
    } = options;

    const requestData = {
      query,
      size,
      latest,
      ignore_cache
    };

    if (pagination_id) {
      requestData.pagination_id = pagination_id;
    }

    // 添加时间范围参数
    if (start_time) {
      requestData.start_time = start_time;
    }
    if (end_time) {
      requestData.end_time = end_time;
    }

    const response = await axios.post(`${QUAKE_BASE_URL}/scroll/quake_service`, requestData, {
      headers: {
        'X-QuakeToken': token,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (response.data.code === 0) {
      return {
        success: true,
        data: response.data.data,
        meta: response.data.meta
      };
    } else {
      return {
        success: false,
        error: response.data.message || '查询失败'
      };
    }
  } catch (error) {
    console.error('Quake深度查询失败:', error.message);
    return {
      success: false,
      error: `网络请求失败: ${error.message}`
    };
  }
}

// 解析Quake返回的数据
function parseQuakeData(rawData, mappingType, queryUsed) {
  const results = [];

  for (const item of rawData) {
    try {
      const result = {
        ip: item.ip || '',
        port: item.port || 0,
        hostname: item.hostname || '',
        transport: item.transport || '',
        asn: item.asn || '',
        org: item.org || '',
        service_name: item.service?.name || '',
        country_cn: item.location?.country_cn || '',
        province_cn: item.location?.province_cn || '',
        city_cn: item.location?.city_cn || '',
        service_response: item.service?.response || '',
        service_cert: item.service?.cert || '',
        time: item.time || '',
        mapping_type: mappingType,
        query_used: queryUsed,
        raw_data: item
      };

      results.push(result);
    } catch (error) {
      console.error('解析数据项失败:', error.message, item);
    }
  }

  return results;
}

// 执行测绘任务
async function executeMappingTask(token, mappingType, options = {}) {
  const {
    maxResults = 1000,
    batchSize = 100,
    onProgress = null,
    onBatchComplete = null,
    startTime = null,
    endTime = null,
    autoMapping = false
  } = options;

  // 验证测绘类型
  if (!MAPPING_MODES[mappingType]) {
    throw new Error(`不支持的测绘类型: ${mappingType}`);
  }

  const mode = MAPPING_MODES[mappingType];
  const query = mode.query;

  console.log(`开始执行测绘任务: ${mode.name}`);
  console.log(`查询语句: ${query}`);

  // 创建任务记录
  const taskId = await saveMappingTask({
    task_name: mode.name,
    mapping_type: mappingType,
    query_statement: query,
    status: 'running',
    start_time: formatDateForMySQL(new Date()),
    time_range_start: startTime,
    time_range_end: endTime,
    auto_mapping: autoMapping ? 1 : 0
  });

  let allResults = [];
  let totalFetched = 0;
  let paginationId = null;
  let hasMore = true;

  try {
    while (hasMore && totalFetched < maxResults) {
      const remainingResults = maxResults - totalFetched;
      const currentBatchSize = Math.min(batchSize, remainingResults);

      // 使用深度查询接口
      const response = await scrollQuakeService(token, query, {
        pagination_id: paginationId,
        size: currentBatchSize,
        latest: true,
        start_time: startTime,
        end_time: endTime
      });

      if (!response.success) {
        throw new Error(response.error);
      }

      const { data, meta } = response;

      if (!data || data.length === 0) {
        hasMore = false;
        break;
      }

      // 解析数据
      const parsedResults = parseQuakeData(data, mappingType, query);
      allResults = allResults.concat(parsedResults);
      totalFetched += data.length;

      // 更新分页ID
      paginationId = meta.pagination_id;

      // 批量保存到数据库
      if (parsedResults.length > 0) {
        await saveBatchMappingResults(parsedResults);
      }

      // 更新任务进度
      await updateMappingTask(taskId, {
        fetched_count: totalFetched,
        pagination_id: paginationId,
        total_count: meta.total?.value || totalFetched
      });

      // 调用进度回调
      if (onProgress) {
        onProgress(totalFetched, meta.total?.value || maxResults);
      }

      // 调用批次完成回调
      if (onBatchComplete) {
        onBatchComplete(parsedResults);
      }

      console.log(`已获取 ${totalFetched} 条数据，本批次 ${data.length} 条`);

      // 避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 任务完成
    await updateMappingTask(taskId, {
      status: 'completed',
      end_time: formatDateForMySQL(new Date()),
      fetched_count: totalFetched
    });

    console.log(`测绘任务完成，共获取 ${totalFetched} 条数据`);

    return {
      success: true,
      taskId,
      totalResults: totalFetched,
      results: allResults
    };

  } catch (error) {
    console.error('测绘任务执行失败:', error.message);

    // 更新任务状态为失败
    await updateMappingTask(taskId, {
      status: 'failed',
      end_time: formatDateForMySQL(new Date()),
      error_message: error.message,
      fetched_count: totalFetched
    });

    return {
      success: false,
      taskId,
      error: error.message,
      totalResults: totalFetched,
      results: allResults
    };
  }
}

// 自动测绘功能 - 从最新时间开始测绘
async function executeAutoMapping(token, mappingType, options = {}) {
  const {
    maxResults = 1000,
    batchSize = 100,
    onProgress = null,
    onBatchComplete = null
  } = options;

  try {
    // 获取该测绘类型的统计信息
    const stats = await getMappingStats(mappingType);

    let startTime = null;
    if (stats.latest_time) {
      // 从最新时间开始，避免重复数据
      startTime = stats.latest_time;
      console.log(`自动测绘从最新时间开始: ${startTime}`);
    } else {
      console.log('首次自动测绘，获取所有数据');
    }

    // 执行测绘任务
    return await executeMappingTask(token, mappingType, {
      maxResults,
      batchSize,
      onProgress,
      onBatchComplete,
      startTime,
      endTime: null, // 不设置结束时间，获取到当前时间的所有数据
      autoMapping: true
    });

  } catch (error) {
    console.error('自动测绘失败:', error.message);
    throw error;
  }
}

// 格式化时间为Quake API格式
function formatTimeForQuake(date) {
  if (!date) return null;

  // 如果是字符串，尝试解析
  if (typeof date === 'string') {
    date = new Date(date);
  }

  // 如果不是有效日期，返回null
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  // 返回ISO格式的时间字符串
  return date.toISOString();
}

// 验证时间范围
function validateTimeRange(startTime, endTime) {
  if (!startTime && !endTime) {
    return { valid: true };
  }

  const start = startTime ? new Date(startTime) : null;
  const end = endTime ? new Date(endTime) : null;

  if (start && isNaN(start.getTime())) {
    return { valid: false, error: '开始时间格式无效' };
  }

  if (end && isNaN(end.getTime())) {
    return { valid: false, error: '结束时间格式无效' };
  }

  if (start && end && start >= end) {
    return { valid: false, error: '开始时间必须早于结束时间' };
  }

  // 检查时间范围是否过大（超过30天）
  if (start && end) {
    const diffDays = (end - start) / (1000 * 60 * 60 * 24);
    if (diffDays > 30) {
      return { valid: false, error: '时间范围不能超过30天' };
    }
  }

  return { valid: true };
}

module.exports = {
  MAPPING_MODES,
  getUserInfo,
  searchQuakeService,
  scrollQuakeService,
  parseQuakeData,
  executeMappingTask,
  executeAutoMapping,
  formatTimeForQuake,
  validateTimeRange
};
