# 仪表盘功能说明

## 📊 功能概述

仪表盘是一个全面的数据可视化和统计分析模块，为Redis扫描和测绘数据提供直观的图表展示和详细的统计报告。

## 🎯 主要特性

### 1. 概览卡片
- **扫描总数**：累计扫描的Redis目标数量
- **成功连接**：成功连接的Redis服务数量
- **挖矿检测**：检测到挖矿活动的服务数量
- **测绘数据**：测绘获取的目标总数

### 2. 多维度图表分析

#### 📈 扫描结果分布
- **图表类型**：饼图/环形图（可切换）
- **数据维度**：成功、失败、挖矿检测
- **功能**：显示扫描结果的整体分布情况

#### 🗺️ 省份分布统计
- **图表类型**：柱状图
- **数据源**：测绘数据/扫描数据（可切换）
- **功能**：展示不同省份的目标分布和成功率

#### 📅 时间趋势分析
- **图表类型**：折线图
- **时间范围**：最近7天/30天/90天（可选择）
- **数据维度**：总扫描数、成功连接、挖矿检测
- **功能**：分析扫描活动的时间趋势

#### 🏢 组织分布TOP10
- **图表类型**：环形图/柱状图（可切换）
- **数据范围**：前10个组织
- **功能**：展示主要云服务商和组织的分布

#### 🔌 端口分布统计
- **图表类型**：柱状图
- **数据范围**：TOP 10/20/50（可选择）
- **数据维度**：总数、成功、挖矿
- **功能**：分析不同端口的使用情况

### 3. 详细统计表格

#### 省份详细统计
| 字段 | 说明 |
|------|------|
| 省份 | 省份名称 |
| 总数 | 该省份的目标总数 |
| 成功 | 成功连接的数量 |
| 挖矿 | 检测到挖矿的数量 |
| 成功率 | 成功连接的百分比 |
| 挖矿率 | 挖矿检测的百分比 |

#### 组织详细统计
| 字段 | 说明 |
|------|------|
| 组织 | 组织名称 |
| 总数 | 该组织的目标总数 |
| 成功 | 成功连接的数量 |
| 挖矿 | 检测到挖矿的数量 |
| 成功率 | 成功连接的百分比 |
| 挖矿率 | 挖矿检测的百分比 |

## 🛠️ 使用方法

### 1. 访问仪表盘
1. 启动应用
2. 点击顶部导航栏的"仪表盘"标签页
3. 系统自动加载最新数据并生成图表

### 2. 刷新数据
- 点击右上角的"刷新数据"按钮
- 系统重新查询数据库并更新所有图表
- 适用于数据更新后需要查看最新统计的场景

### 3. 图表交互
- **扫描结果分布**：切换饼图/环形图显示
- **省份分布**：切换测绘数据/扫描数据源
- **时间趋势**：选择不同的时间范围
- **组织分布**：切换环形图/柱状图视图
- **端口分布**：选择显示的数量范围

### 4. 数据导出

#### 导出仪表盘报告
1. 点击"导出报告"按钮
2. 系统生成包含所有统计数据的Excel文件
3. 文件包含5个工作表：
   - 概览：基础统计数据
   - 省份统计：省份详细数据
   - 组织统计：组织详细数据
   - 端口统计：端口详细数据
   - 时间趋势：时间序列数据

#### 导出统计表格
1. 在省份或组织统计表格右上角点击"导出"按钮
2. 导出当前表格的详细数据为Excel文件

## 📋 技术实现

### 1. 前端技术
- **图表库**：Chart.js - 提供丰富的图表类型和交互功能
- **样式**：CSS Grid + Flexbox - 响应式布局设计
- **交互**：原生JavaScript - 图表控制和数据更新

### 2. 后端数据
- **数据源**：SQLite数据库
- **查询优化**：使用索引和聚合查询提高性能
- **数据处理**：实时计算统计指标和百分比

### 3. 数据统计函数
```javascript
// 主要统计函数
getDashboardStats()     // 基础统计数据
getProvinceStats()      // 省份统计
getOrgStats()          // 组织统计
getPortStats()         // 端口统计
getTimeTrendStats()    // 时间趋势
```

## 🎨 界面设计

### 1. 布局结构
```
仪表盘头部（标题 + 操作按钮）
├── 概览卡片区域（4个统计卡片）
├── 图表区域
│   ├── 第一行：扫描结果分布 + 省份分布
│   ├── 第二行：时间趋势 + 组织分布
│   └── 第三行：端口分布（全宽）
└── 统计表格区域
    ├── 省份详细统计表
    └── 组织详细统计表
```

### 2. 响应式设计
- **桌面端**：2列网格布局，图表并排显示
- **平板端**：1列布局，图表垂直排列
- **移动端**：紧凑布局，优化触摸操作

### 3. 颜色方案
- **成功**：绿色 (#28a745)
- **失败**：红色 (#dc3545)
- **挖矿**：黄色 (#ffc107)
- **主色调**：蓝色 (#007bff)

## 📈 数据指标说明

### 1. 成功率计算
```
成功率 = (成功连接数 / 总扫描数) × 100%
```

### 2. 挖矿率计算
```
挖矿率 = (挖矿检测数 / 成功连接数) × 100%
```

### 3. 测试率计算
```
测试率 = (已测试数 / 测绘总数) × 100%
```

## 🔧 配置选项

### 1. 图表配置
- 图表类型可通过下拉菜单切换
- 数据源可在测绘数据和扫描数据间切换
- 时间范围支持7天、30天、90天选择
- 端口统计支持TOP 10/20/50显示

### 2. 导出配置
- 支持Excel格式导出
- 文件名自动包含时间戳
- 多工作表结构化数据

## 🚀 性能优化

### 1. 数据查询优化
- 使用数据库索引加速查询
- 聚合查询减少数据传输
- 异步加载避免界面阻塞

### 2. 图表渲染优化
- Chart.js响应式配置
- 数据更新时销毁重建图表
- 合理的图表尺寸和动画设置

### 3. 内存管理
- 图表实例正确销毁
- 避免内存泄漏
- 合理的数据缓存策略

## 📝 使用建议

### 1. 最佳实践
- 定期刷新数据查看最新统计
- 结合不同图表分析数据趋势
- 导出报告用于离线分析和汇报

### 2. 注意事项
- 大量数据时图表加载可能较慢
- 建议在数据更新后手动刷新仪表盘
- 导出功能需要足够的磁盘空间

### 3. 故障排除
- 如果图表不显示，检查数据库连接
- 如果导出失败，检查文件权限
- 如果数据不准确，尝试刷新数据

仪表盘为Redis安全扫描提供了全面的数据洞察能力，帮助用户更好地理解扫描结果和安全态势！🎉
