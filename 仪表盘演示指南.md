# 🎯 仪表盘演示指南

## 📋 演示准备

### 1. 确认应用状态
✅ 应用已启动并运行
✅ 数据库已初始化
✅ 测试数据已创建（包含扫描结果和测绘数据）

### 2. 数据概况
根据测试结果，当前数据库包含：
- **扫描结果**：589条记录
- **成功连接**：108个Redis服务
- **挖矿检测**：63个挖矿活动
- **测绘数据**：105条记录
- **省份覆盖**：17个省份
- **组织覆盖**：14个主要组织

## 🎬 演示步骤

### 第一步：访问仪表盘
1. 在应用界面顶部，点击"仪表盘"标签页
2. 观察页面自动加载数据和生成图表
3. 注意概览卡片显示的关键指标

### 第二步：概览卡片展示
指出四个主要指标卡片：
- 📊 **扫描总数**: 589（累计扫描目标）
- ✅ **成功连接**: 108（可访问Redis）
- ⚠️ **挖矿检测**: 63（发现挖矿活动）
- 🗺️ **测绘数据**: 105（测绘目标总数）

### 第三步：图表功能演示

#### 1. 扫描结果分布图
- **位置**：左上角
- **功能**：展示成功、失败、挖矿的分布
- **交互**：切换饼图/环形图
- **演示**：点击下拉菜单切换图表类型

#### 2. 省份分布统计图
- **位置**：右上角
- **功能**：显示不同省份的目标分布
- **交互**：切换测绘数据/扫描数据源
- **演示**：
  1. 默认显示测绘数据（北京、上海、广东等）
  2. 切换到扫描数据，观察数据变化

#### 3. 时间趋势分析图
- **位置**：左中
- **功能**：显示扫描活动的时间趋势
- **交互**：选择7天/30天/90天
- **演示**：
  1. 默认显示7天趋势
  2. 切换到30天，观察更长时间的趋势

#### 4. 组织分布TOP10图
- **位置**：右中
- **功能**：展示主要云服务商分布
- **交互**：切换环形图/柱状图
- **演示**：
  1. 观察阿里云、腾讯云等主要厂商
  2. 点击"切换视图"按钮改变图表类型

#### 5. 端口分布统计图
- **位置**：底部全宽
- **功能**：分析不同端口的使用情况
- **交互**：选择TOP 10/20/50
- **演示**：
  1. 观察6379端口占主导地位
  2. 切换显示更多端口数据

### 第四步：统计表格展示

#### 1. 省份详细统计表
- **位置**：左下角
- **内容**：省份、总数、成功、挖矿、成功率、挖矿率
- **演示**：
  1. 指出北京、上海等重点省份
  2. 分析成功率和挖矿率的差异

#### 2. 组织详细统计表
- **位置**：右下角
- **内容**：组织、总数、成功、挖矿、成功率、挖矿率
- **演示**：
  1. 观察阿里云、腾讯云的数据
  2. 分析不同云厂商的安全状况

### 第五步：数据刷新功能
1. 点击右上角"刷新数据"按钮
2. 观察按钮状态变为"加载中..."
3. 等待数据重新加载完成
4. 说明：实际使用中，扫描新数据后可用此功能更新统计

### 第六步：导出功能演示

#### 1. 导出仪表盘报告
1. 点击"导出报告"按钮
2. 观察按钮状态变为"生成中..."
3. 等待Excel文件生成完成
4. 说明报告包含5个工作表的完整数据

#### 2. 导出统计表格
1. 在省份统计表右上角点击"导出"
2. 生成省份详细统计的Excel文件
3. 同样可以导出组织统计数据

## 🎯 演示重点

### 1. 数据洞察能力
- **地理分布**：北京、上海、广东是主要目标区域
- **厂商分析**：阿里云、腾讯云占据主要份额
- **安全态势**：挖矿检测率达58.3%，安全形势严峻
- **端口分析**：6379端口是主要攻击目标

### 2. 交互体验
- **实时响应**：图表切换流畅无延迟
- **多维分析**：支持不同数据源和时间范围
- **直观展示**：颜色编码清晰，数据一目了然

### 3. 实用功能
- **数据导出**：支持Excel格式，便于离线分析
- **实时更新**：一键刷新获取最新统计
- **响应式设计**：适配不同屏幕尺寸

## 📊 数据解读示例

### 1. 安全态势分析
```
总体安全状况：
- 连接成功率：18.3%（108/589）
- 挖矿检测率：58.3%（63/108）
- 风险评估：高风险，超过一半的可访问Redis存在挖矿活动
```

### 2. 地域分布特点
```
重点关注区域：
- 北京：31个目标，100%连接成功，64.5%存在挖矿
- 上海：24个目标，100%连接成功，70.8%存在挖矿
- 广东：19个目标，100%连接成功，57.9%存在挖矿
```

### 3. 厂商安全对比
```
主要云厂商对比：
- 阿里云：36个目标，33.3%挖矿率
- 腾讯云：32个目标，78.1%挖矿率
- 电信云：16个目标，75.0%挖矿率
```

## 🎉 演示总结

仪表盘功能为Redis安全扫描提供了：

1. **全面的数据可视化**：多种图表类型展示不同维度数据
2. **深入的统计分析**：省份、组织、端口、时间等多维度分析
3. **便捷的交互操作**：图表切换、数据源选择、时间范围调整
4. **实用的导出功能**：Excel报告生成，支持离线分析
5. **实时的数据更新**：一键刷新，获取最新统计信息

这个仪表盘将帮助安全分析师更好地理解Redis安全态势，制定针对性的安全策略！🚀

## 💡 使用建议

1. **定期查看**：建议每次扫描后查看仪表盘了解最新态势
2. **多维分析**：结合不同图表和数据源进行综合分析
3. **导出报告**：定期导出数据用于汇报和存档
4. **趋势监控**：关注时间趋势图，发现安全态势变化
5. **重点关注**：重点关注挖矿率高的地区和组织
