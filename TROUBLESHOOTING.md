# 多线程扫描故障排除指南

## 常见问题及解决方案

### 1. Worker Threads 不支持错误

**错误信息**: `Failed to construct 'Worker': The V8 platform used by this instance of Node does not support creating Workers`

**原因**: Electron渲染进程默认不支持Worker Threads

**解决方案**: 
- ✅ 已实现：使用IPC（进程间通信）方案
- 多线程扫描在主进程中执行
- 渲染进程通过IPC与主进程通信
- 自动回退到传统扫描方式

### 2. 多线程扫描失效

**症状**: 启用多线程但仍使用传统扫描

**可能原因**:
1. 目标数量为1（单个目标自动使用单线程）
2. 多线程开关未启用
3. IPC通信失败

**检查步骤**:
1. 确认设置页面中"启用多线程扫描"已勾选
2. 确认目标数量大于1
3. 查看控制台日志确认扫描方式

### 3. 性能没有提升

**可能原因**:
1. 网络延迟是瓶颈（不是CPU密集型任务）
2. 线程数设置不当
3. 目标服务器限制并发连接

**优化建议**:
1. 根据网络环境调整线程数（建议4-8个）
2. 监控系统资源使用情况
3. 考虑目标服务器的负载能力

### 4. 内存使用过高

**原因**: 过多的并发连接和Worker线程

**解决方案**:
1. 减少线程数量
2. 监控系统内存使用
3. 及时清理扫描结果

### 5. 扫描中断或崩溃

**可能原因**:
1. Worker线程异常
2. 内存不足
3. 网络连接问题

**预防措施**:
1. 启用错误恢复机制（已实现）
2. 设置合理的超时时间
3. 监控系统资源

## 调试方法

### 1. 启用详细日志
在开发者工具控制台中查看详细日志：
```javascript
// 查看扫描方式
console.log('扫描方式:', useMultiThread ? '多线程' : '传统');

// 查看线程池状态
console.log('线程池状态:', threadPool.getStatus());
```

### 2. 测试多线程功能
运行测试脚本：
```bash
npm run test-multithread
```

### 3. 性能对比测试
运行性能对比脚本：
```bash
node performance-test.js
```

## 配置建议

### 线程数量设置
- **低端设备** (2-4核): 2-4个线程
- **中端设备** (4-8核): 4-6个线程  
- **高端设备** (8核+): 6-8个线程
- **服务器环境**: 8-16个线程

### 网络环境考虑
- **局域网扫描**: 可以使用较多线程
- **互联网扫描**: 建议减少线程数，避免触发防护
- **高延迟网络**: 多线程优势明显

## 回退机制

当多线程扫描失败时，系统会自动：
1. 显示错误信息
2. 回退到传统异步并发扫描
3. 继续完成扫描任务
4. 保持用户体验的连续性

## 监控和诊断

### 系统资源监控
- CPU使用率
- 内存使用量
- 网络连接数
- 磁盘I/O

### 扫描性能指标
- 扫描速度（目标/秒）
- 成功率
- 错误率
- 平均响应时间

## 联系支持

如果遇到其他问题，请：
1. 收集错误日志
2. 记录系统环境信息
3. 描述重现步骤
4. 提供扫描配置信息
