0x05 用户信息
接口地址：/api/v3/user/info
请求方式：GET
接口描述：​ 获取用户详情
接口参数
​ Headers

字段	类型	必填	示例	备注
X-QuakeToken	str	是	d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx	用户API调用私钥
使用范例
shell请求范例
curl -X GET "https://quake.360.net/api/v3/user/info" -H "X-QuakeToken: d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx"
python请求范例
import requests

headers = {
    "X-QuakeToken": "d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx"
}

response = requests.get(url="https://quake.360.net/api/v3/user/info", headers=headers)
print(response.json())
返回值范例
{
  "code": 0,
  "message": "Successful",
  "data": {
    "id": "5ea7e45147bc09xxxxxxxxcb",
    "user": {
      "id": "5ea7e45147bc09xxxxxxxxc8",
      "username": "xxxxxxx",
      "fullname": "XX",
      "email": "<EMAIL>"
    },
    "baned": false,
    "ban_status": "使用中",
    "credit": 5000,
    "persistent_credit": 1000,
    "token": "xxxx-xxxx",
    "mobile_phone": "",
    "source": "quake",
    "privacy_log": {
      "status": false,
      "time": null
    },
    "enterprise_information": {
      "name": null,
      "email": null,
      "status": "未认证"
    },
    "personal_information_status": false,
    "role": [
      {
        "fullname": "注册用户",
        "priority": 4,
        "credit": 3000
      }
    ]
  },
  "meta": {}
}