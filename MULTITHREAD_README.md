# Redis未授权访问检测工具 - 多线程扫描功能

## 概述

本工具已升级支持多线程批量Redis扫描，可以显著提高大规模扫描的性能。

## 新增功能

### 1. 多线程扫描引擎
- 基于Node.js Worker Threads实现真正的多线程并发
- 支持自定义线程数量（1-16个线程）
- 自动根据CPU核心数推荐最佳线程数
- 独立的Worker进程，避免主线程阻塞

### 2. 智能任务调度
- 线程池管理，自动分配和回收Worker线程
- 任务队列机制，确保高效的任务分发
- 错误隔离，单个线程异常不影响整体扫描
- 优雅的线程池关闭和资源清理

### 3. 实时进度跟踪
- 实时显示扫描进度和统计信息
- 支持进度回调和单个扫描完成回调
- 线程状态监控和性能统计

## 使用方法

### 1. 界面配置
1. 打开工具，切换到"设置"页面
2. 在"扫描性能设置"部分：
   - 勾选"启用多线程扫描"
   - 设置"线程数量"（建议设置为CPU核心数）
3. 点击"保存设置"

### 2. 批量扫描
1. 切换到"扫描"页面
2. 选择"批量扫描"
3. 输入目标列表或导入文件
4. 点击"开始扫描"

当目标数量大于1且启用了多线程时，系统会自动使用多线程扫描。

### 3. 性能对比
- **传统异步并发**: 使用Promise.all()进行批量处理，受事件循环限制
- **多线程扫描**: 使用Worker Threads，真正的并行处理，性能提升显著

## 技术实现

### 架构设计
```
Electron主进程 (Main Process)
├── 线程池管理器 (ThreadPool)
├── 任务调度器 (Task Scheduler)
├── Worker线程管理
└── IPC通信处理

Worker线程 (Worker Threads)
├── Redis连接处理
├── 数据获取和分析
├── 勒索特征检测
└── 结果返回

Electron渲染进程 (Renderer Process)
├── UI界面管理
├── 配置管理
├── IPC通信
└── 进度显示
```

### 解决方案说明
由于Electron渲染进程对Worker Threads的支持有限，本实现采用了IPC（进程间通信）方案：
- 渲染进程负责UI交互和配置管理
- 主进程负责执行多线程扫描任务
- 通过IPC实现进度和结果的实时传递

### 核心文件
- `src/redis-scanner.js` - 主扫描逻辑和多线程函数
- `src/thread-pool.js` - 线程池管理器
- `src/worker/redis-worker.js` - Worker线程脚本
- `src/renderer.js` - UI交互和配置管理

### 关键函数
- `batchScanRedisMultiThread()` - 多线程批量扫描主函数
- `batchScanRedisMultiThreadWithProgress()` - 带进度回调的多线程扫描
- `ThreadPool` - 线程池管理类

## 配置参数

### 线程数量建议
- **CPU核心数 ≤ 4**: 建议使用4个线程
- **CPU核心数 4-8**: 建议使用CPU核心数
- **CPU核心数 > 8**: 建议使用8个线程（最多16个）

### 性能优化
- 单个目标扫描仍使用单线程（避免不必要的开销）
- 自动去重机制，避免重复扫描相同目标
- 内存优化，及时清理Worker线程资源

## 测试

运行测试脚本验证多线程功能：
```bash
node test-multithread.js
```

## 注意事项

1. **系统要求**: Node.js 12.0+ (支持Worker Threads)
2. **内存使用**: 多线程会增加内存使用，建议监控系统资源
3. **网络限制**: 过多并发可能触发目标系统的防护机制
4. **错误处理**: Worker线程异常会自动重启，不影响整体扫描

## 性能提升

在测试环境中，多线程扫描相比传统方式：
- **扫描速度**: 提升2-4倍（取决于CPU核心数和网络条件）
- **资源利用**: 更好的CPU利用率
- **响应性**: 主线程不阻塞，UI保持响应

## 兼容性

- 完全向后兼容，现有功能不受影响
- 可以随时在传统扫描和多线程扫描之间切换
- 扫描结果格式保持一致
