const { initDb } = require('./src/database');
const { MAPPING_MODES, getUserInfo, searchQuakeService } = require('./src/quake-mapper');

async function testMappingModule() {
  console.log('测试Quake测绘模块...');
  console.log('='.repeat(50));
  
  try {
    // 初始化数据库
    await initDb();
    console.log('✓ 数据库初始化完成');
    
    // 显示可用的测绘模式
    console.log('\n可用的测绘模式:');
    for (const [key, mode] of Object.entries(MAPPING_MODES)) {
      console.log(`- ${key}: ${mode.name}`);
      console.log(`  查询语句: ${mode.query}`);
      console.log(`  描述: ${mode.description}\n`);
    }
    
    // 测试Token验证（需要用户提供真实Token）
    const testToken = process.env.QUAKE_TOKEN || 'your_test_token_here';
    
    if (testToken && testToken !== 'your_test_token_here') {
      console.log('测试Token验证...');
      const userResult = await getUserInfo(testToken);
      
      if (userResult.success) {
        console.log('✓ Token验证成功');
        console.log(`  用户: ${userResult.data.user.username}`);
        console.log(`  剩余积分: ${userResult.data.credit}`);
        
        // 测试小规模查询
        console.log('\n测试小规模查询...');
        const searchResult = await searchQuakeService(testToken, MAPPING_MODES.redis_unauthorized.query, {
          start: 0,
          size: 5 // 只获取5条数据进行测试
        });
        
        if (searchResult.success) {
          console.log('✓ 查询成功');
          console.log(`  返回数据数量: ${searchResult.data.length}`);
          console.log(`  总数据量: ${searchResult.meta.total?.value || '未知'}`);
          
          if (searchResult.data.length > 0) {
            console.log('\n示例数据:');
            const sample = searchResult.data[0];
            console.log(`  IP: ${sample.ip}`);
            console.log(`  端口: ${sample.port}`);
            console.log(`  位置: ${sample.location?.country_cn} ${sample.location?.province_cn} ${sample.location?.city_cn}`);
            console.log(`  组织: ${sample.org}`);
            console.log(`  服务: ${sample.service?.name}`);
          }
        } else {
          console.log('✗ 查询失败:', searchResult.error);
        }
      } else {
        console.log('✗ Token验证失败:', userResult.error);
      }
    } else {
      console.log('⚠ 跳过Token测试（未提供有效Token）');
      console.log('  要测试Token功能，请设置环境变量: QUAKE_TOKEN=your_actual_token');
    }
    
    console.log('\n测绘模块基础功能测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 测试数据解析功能
function testDataParsing() {
  console.log('\n测试数据解析功能...');
  
  const { parseQuakeData } = require('./src/quake-mapper');
  
  // 模拟Quake API返回的数据
  const mockData = [
    {
      ip: '*************',
      port: 6379,
      hostname: 'test.example.com',
      transport: 'tcp',
      asn: 'AS4134',
      org: 'China Telecom',
      service: {
        name: 'redis',
        response: 'redis_version:6.2.0'
      },
      location: {
        country_cn: '中国',
        province_cn: '北京',
        city_cn: '北京'
      },
      time: '2024-01-01T12:00:00Z'
    }
  ];
  
  const parsed = parseQuakeData(mockData, 'redis_unauthorized', 'test_query');
  
  if (parsed.length > 0) {
    console.log('✓ 数据解析成功');
    console.log('  解析结果:', JSON.stringify(parsed[0], null, 2));
  } else {
    console.log('✗ 数据解析失败');
  }
}

// 运行测试
if (require.main === module) {
  testMappingModule().then(() => {
    testDataParsing();
    console.log('\n所有测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testMappingModule, testDataParsing };
