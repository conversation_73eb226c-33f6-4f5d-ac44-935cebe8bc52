// 测试Electron环境下的多线程扫描功能
const { app, BrowserWindow } = require('electron');
const { batchScanRedisMultiThread } = require('./src/redis-scanner');
const { initDb } = require('./src/database');

async function testElectronMultiThread() {
  console.log('测试Electron环境下的多线程Redis扫描...');
  
  try {
    // 初始化数据库
    await initDb();
    console.log('数据库初始化完成');
    
    // 测试目标列表
    const targets = [
      '127.0.0.1:6379',
      '*************:6379',
      '*************:6379',
      '**********:6379',
      '************:6379'
    ];
    
    console.log(`测试目标: ${targets.join(', ')}`);
    
    // 进度回调函数
    const onProgress = (completed, total) => {
      const percent = ((completed / total) * 100).toFixed(1);
      console.log(`进度: ${completed}/${total} (${percent}%)`);
    };
    
    // 单个扫描完成回调
    const onScanComplete = (result) => {
      console.log(`扫描完成: ${result.host}:${result.port} - ${result.status === 1 ? '成功' : '失败'}`);
    };
    
    // 执行多线程扫描
    console.log('开始多线程扫描...');
    const startTime = Date.now();
    
    const results = await batchScanRedisMultiThread(targets, {
      maxThreads: 4,
      onProgress,
      onScanComplete
    });
    
    const endTime = Date.now();
    
    console.log('\n=== 扫描结果 ===');
    console.log(`总耗时: ${endTime - startTime}ms`);
    console.log(`扫描目标数: ${targets.length}`);
    console.log(`返回结果数: ${results.length}`);
    
    const successCount = results.filter(r => r.status === 1).length;
    const failCount = results.filter(r => r.status === 0).length;
    
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    
    console.log('\n多线程扫描测试完成！');
    
  } catch (error) {
    console.error('测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

// 当Electron准备就绪时运行测试
app.whenReady().then(async () => {
  console.log('Electron应用已准备就绪');
  
  try {
    await testElectronMultiThread();
    console.log('测试完成，退出应用');
    app.quit();
  } catch (error) {
    console.error('测试过程中出错:', error);
    app.quit();
  }
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  app.quit();
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  app.quit();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  app.quit();
});
