const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// 导出扫描结果到Excel文件
async function exportToExcel(data, filePath) {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('扫描结果');
    
    // 设置表头
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '主机地址', key: 'host', width: 20 },
      { header: '端口', key: 'port', width: 10 },
      { header: '连接状态', key: 'status', width: 10 },
      { header: '连接时间', key: 'connect_time', width: 25 },
      { header: '状态信息', key: 'status_info', width: 40 },
      { header: '是否勒索', key: 'is_ransomware', width: 10 },
      { header: '自动检测', key: 'auto_detected', width: 10 },
      { header: '键值数据', key: 'key_values', width: 50 }
    ];
    
    // 添加数据
    data.forEach(item => {
      worksheet.addRow({
        id: item.id,
        host: item.host,
        port: item.port,
        status: item.status === 1 ? '成功' : '失败',
        connect_time: item.connect_time,
        status_info: item.status_info,
        is_ransomware: item.is_ransomware === 1 ? '是' : '否',
        auto_detected: item.auto_detected === 1 ? '是' : '否',
        key_values: JSON.stringify(item.key_values)
      });
    });
    
    // 设置表头样式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
    
    // 保存文件
    await workbook.xlsx.writeFile(filePath);
    
    return { success: true, message: `导出成功: ${filePath}` };
  } catch (err) {
    console.error('导出Excel失败:', err.message);
    return { success: false, message: `导出失败: ${err.message}` };
  }
}

module.exports = {
  exportToExcel
}; 