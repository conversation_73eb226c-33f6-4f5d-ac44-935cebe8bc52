const { initDb, getMappingStats, getUntestedMappingResults, markMappingResultsAsTested } = require('./src/database');
const { validateTimeRange, formatTimeForQuake } = require('./src/quake-mapper');

async function demoNewFeatures() {
  console.log('🚀 演示Quake测绘模块新功能');
  console.log('='.repeat(50));
  
  try {
    // 初始化数据库
    await initDb();
    console.log('✓ 数据库初始化完成');
    
    // 演示1: 测绘统计信息
    console.log('\n📊 演示1: 测绘统计信息');
    console.log('-'.repeat(30));
    
    const stats = await getMappingStats();
    console.log('测绘统计:');
    console.log(`  总数量: ${stats.total_count}`);
    console.log(`  已测试: ${stats.tested_count}`);
    console.log(`  未测试: ${stats.untested_count}`);
    console.log(`  最新时间: ${stats.latest_time || '无数据'}`);
    console.log(`  最早时间: ${stats.earliest_time || '无数据'}`);
    
    // 演示2: 获取未测试目标
    console.log('\n🎯 演示2: 获取未测试目标');
    console.log('-'.repeat(30));
    
    const untestedResults = await getUntestedMappingResults(null, 5); // 只获取5条示例
    console.log(`未测试目标数量: ${untestedResults.length}`);
    
    if (untestedResults.length > 0) {
      console.log('示例未测试目标:');
      untestedResults.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.ip}:${result.port} (${result.country_cn} ${result.province_cn})`);
      });
      
      // 演示标记为已测试
      const targets = untestedResults.map(r => `${r.ip}:${r.port}`);
      console.log('\n标记这些目标为已测试...');
      const markedCount = await markMappingResultsAsTested(targets);
      console.log(`✓ 已标记 ${markedCount} 个目标为已测试`);
    } else {
      console.log('当前没有未测试的目标');
    }
    
    // 演示3: 时间范围验证
    console.log('\n⏰ 演示3: 时间范围验证');
    console.log('-'.repeat(30));
    
    // 测试有效时间范围
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const validRange = validateTimeRange(
      formatTimeForQuake(oneWeekAgo),
      formatTimeForQuake(now)
    );
    console.log('测试一周时间范围:');
    console.log(`  开始时间: ${formatTimeForQuake(oneWeekAgo)}`);
    console.log(`  结束时间: ${formatTimeForQuake(now)}`);
    console.log(`  验证结果: ${validRange.valid ? '✓ 有效' : '✗ 无效'}`);
    if (!validRange.valid) {
      console.log(`  错误信息: ${validRange.error}`);
    }
    
    // 测试无效时间范围（超过30天）
    const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    const invalidRange = validateTimeRange(
      formatTimeForQuake(twoMonthsAgo),
      formatTimeForQuake(now)
    );
    console.log('\n测试两个月时间范围:');
    console.log(`  开始时间: ${formatTimeForQuake(twoMonthsAgo)}`);
    console.log(`  结束时间: ${formatTimeForQuake(now)}`);
    console.log(`  验证结果: ${invalidRange.valid ? '✓ 有效' : '✗ 无效'}`);
    if (!invalidRange.valid) {
      console.log(`  错误信息: ${invalidRange.error}`);
    }
    
    // 演示4: 模拟用户积分信息
    console.log('\n💰 演示4: 用户积分信息显示');
    console.log('-'.repeat(30));
    
    const mockUserData = {
      user: {
        username: 'demo_user'
      },
      credit: 1000,
      persistent_credit: 500
    };
    
    console.log('模拟用户信息:');
    console.log(`  用户名: ${mockUserData.user.username}`);
    console.log(`  普通积分: ${mockUserData.credit}`);
    console.log(`  长效积分: ${mockUserData.persistent_credit}`);
    console.log(`  总积分: ${mockUserData.credit + mockUserData.persistent_credit}`);
    
    // 演示5: 自动测绘逻辑
    console.log('\n🤖 演示5: 自动测绘逻辑');
    console.log('-'.repeat(30));
    
    if (stats.latest_time) {
      console.log('自动测绘配置:');
      console.log(`  最新数据时间: ${stats.latest_time}`);
      console.log(`  建议开始时间: ${stats.latest_time} (从最新时间开始)`);
      console.log(`  结束时间: ${formatTimeForQuake(now)} (到当前时间)`);
      console.log('  策略: 增量获取，避免重复数据');
    } else {
      console.log('自动测绘配置:');
      console.log('  首次测绘: 获取所有历史数据');
      console.log('  策略: 全量获取');
    }
    
    // 演示6: 功能使用建议
    console.log('\n💡 演示6: 功能使用建议');
    console.log('-'.repeat(30));
    
    console.log('推荐使用流程:');
    console.log('1. 验证Token，查看积分情况');
    console.log('2. 首次使用普通测绘获取基础数据');
    console.log('3. 定期使用自动测绘获取增量数据');
    console.log('4. 导出未测试目标进行安全扫描');
    console.log('5. 扫描完成后查看测试覆盖率');
    
    console.log('\n注意事项:');
    console.log('• 合理设置时间范围，避免超过30天限制');
    console.log('• 监控积分消耗，特别是长效积分的使用');
    console.log('• 定期清理过期的测绘数据');
    console.log('• 使用自动测绘提高数据获取效率');
    
    console.log('\n🎉 新功能演示完成！');
    console.log('现在可以启动应用体验完整功能: npm start');
    
  } catch (error) {
    console.error('演示过程中出错:', error.message);
  }
}

// 运行演示
if (require.main === module) {
  demoNewFeatures().then(() => {
    console.log('\n演示结束');
    process.exit(0);
  }).catch(error => {
    console.error('演示失败:', error);
    process.exit(1);
  });
}

module.exports = { demoNewFeatures };
