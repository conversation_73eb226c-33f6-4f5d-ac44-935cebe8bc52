const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();
const { initDb } = require('./src/database');
const { batchScanRedisMultiThread } = require('./src/redis-scanner');

// 保持对window对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow;

async function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      nodeIntegrationInWorker: true, // 启用Worker中的Node.js集成
      experimentalFeatures: true // 启用实验性功能
    }
  });

  // 加载应用的index.html
  mainWindow.loadFile('src/index.html');

  // 初始化数据库
  await initDb();

  // 打开开发者工具
  // mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', function() {
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(createWindow);

// 当所有窗口关闭时退出应用
app.on('window-all-closed', function() {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function() {
  if (mainWindow === null) createWindow();
});

// 处理文件选择
ipcMain.handle('select-file', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [{ name: 'Text Files', extensions: ['txt'] }]
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const filePath = result.filePaths[0];
    const content = fs.readFileSync(filePath, 'utf8');
    return content;
  }
  return null;
});

// 处理导出功能
ipcMain.handle('export-data', async (event, data, type) => {
  const result = await dialog.showSaveDialog({
    title: '导出扫描结果',
    defaultPath: `redis_scan_result_${type}_${new Date().toISOString().replace(/[:.]/g, '-')}.xlsx`,
    filters: [{ name: 'Excel Files', extensions: ['xlsx'] }]
  });

  if (!result.canceled) {
    // 在renderer进程中处理导出逻辑
    return result.filePath;
  }
  return null;
});

// 处理多线程扫描请求
ipcMain.handle('batch-scan-multithread', async (event, targets, options) => {
  try {
    console.log('主进程收到多线程扫描请求，目标数量:', targets.length);

    // 创建进度回调，将进度发送到渲染进程
    const onProgress = (completed, total) => {
      event.sender.send('scan-progress', { completed, total });
    };

    // 创建单个扫描完成回调
    const onScanComplete = (result) => {
      event.sender.send('scan-complete', result);
    };

    // 在主进程中执行多线程扫描
    const results = await batchScanRedisMultiThread(targets, {
      ...options,
      onProgress,
      onScanComplete
    });

    console.log('主进程多线程扫描完成，结果数量:', results.length);
    return { success: true, results };

  } catch (error) {
    console.error('主进程多线程扫描失败:', error);
    return { success: false, error: error.message };
  }
});