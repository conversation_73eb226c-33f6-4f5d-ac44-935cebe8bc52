# 程序介绍
编写一个EXE图形化程序，程序的基本功能是实现对单个或批量redis服务的未授权检测。
# 程序功能
1、实现单个redis服务的未授权检测
2、实现批量redis服务的未授权检测
3、若存在未授权，获取到前几个集合的key-value
4、提供扫描结果展示界面，展示扫描结果（按连接成功与否分类展示，以连接成功的为主）：
    1、redis服务地址
    2、redis服务端口
    3、redis服务连接状态
    4、redis服务连接时间
    5、redis服务连接状态信息
    6、redis服务连接成功后，获取到前几个集合的key-value
    7、提供操作按钮，可以根据key-value人工研判是否已被勒索，并自动对该条数据进行标记
    8、根据以往标注的勒索特征数据，对扫描结果进行自动研判，并自动对该条数据进行标记
5、所有扫描结果数据保存在本地（以成功与否分类），并提供导出功能（导出为xlsx文件）
6、每次扫描结果尝试更新到远程服务器（通过API的形式），并提供远程服务器地址配置（若无法连接，则不更新）

# 程序实现
1、使用electron框架实现图形化界面
2、打包为exe文件
3、使用sqlite存储扫描数据，存储在项目根目录/db/redis_unauthorized.db
4、使用sqlite存储配置信息，存储在项目根目录/db/config.db
5、使用redis驱动库

# 程序运行
1、运行程序
2、配置远程服务器地址
3、配置redis服务地址和端口，可复制粘贴，也可以导入txt文档的形式（txt文档中每行一个redis服务地址和端口，格式为：地址:端口）
4、点击开始扫描按钮
5、扫描结果展示界面
