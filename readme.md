# Redis未授权访问检测工具

这是一个基于Electron的图形化工具，用于检测Redis服务的未授权访问漏洞。

## 功能特性

### 核心扫描功能
1. **单个Redis服务检测**: 检测单个Redis服务的未授权访问
2. **批量扫描**: 支持批量扫描多个Redis目标
3. **多线程扫描**: 支持多线程并发扫描，2-4倍性能提升
4. **数据获取**: 获取Redis中的键值对数据
5. **勒索软件检测**: 自动检测Redis中的勒索软件特征

### 🆕 测绘引擎集成
6. **Quake测绘**: 集成Quake测绘引擎，自动获取Redis服务目标
7. **预定义模式**: 境内Redis未授权、境内Redis挖矿两种测绘模式
8. **长效积分显示**: 显示Quake账户的普通积分和长效积分
9. **自定义时间范围**: 支持指定时间范围进行测绘
10. **自动测绘**: 智能增量测绘，从最新时间开始避免重复

### 🆕 测试状态管理
11. **测试状态跟踪**: 记录和显示测绘结果的测试状态
12. **未测试目标管理**: 提取和导出未测试的目标
13. **一键发送扫描**: 批量将未测试目标添加到扫描列表
14. **测试覆盖率**: 实时显示已测试和未测试目标统计

### 数据管理
15. **结果导出**: 支持将扫描结果导出为Excel文件
16. **远程同步**: 支持将结果上传到远程服务器
17. **特征管理**: 支持自定义勒索软件检测特征
18. **智能去重**: 自动去重，避免重复数据

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/redis-unauthorized-scanner.git

# 进入目录
cd redis-unauthorized-scanner

# 安装依赖
npm install
```

## 运行

```bash
# 开发模式运行
npm start

# 构建可执行文件
npm run build
```

构建后的可执行文件将在`dist`目录下。

## 测试

```bash
# 测试多线程扫描功能
npm run test-multithread

# 测试测绘模块基础功能
npm run test-mapping

# 演示新功能
npm run demo-features

# 最终功能验证
npm run verify
```

## 使用说明

### 基础扫描功能
1. 启动程序后，可以选择单个扫描或批量扫描模式
2. 输入Redis服务地址和端口，或导入包含多个目标的文本文件
3. 点击"开始扫描"按钮开始检测
4. 在"结果"标签页查看扫描结果，可以按连接状态和挖矿标记进行筛选
5. 点击"查看详情"按钮查看Redis服务的详细信息和键值数据
6. 在"设置"标签页配置远程API服务器地址和管理挖矿特征

### 🆕 测绘功能使用
1. **配置Quake Token**: 在"测绘"或"设置"页面输入Quake API Token
2. **验证Token**: 点击"验证Token"查看用户信息和积分（包括长效积分）
3. **选择测绘模式**:
   - 境内Redis未授权：检测所有未授权Redis服务
   - 境内Redis挖矿：检测疑似挖矿Redis服务
4. **设置参数**: 配置最大结果数、批次大小、时间范围（可选）
5. **执行测绘**:
   - 普通测绘：指定参数进行测绘
   - 自动测绘：智能增量测绘，从最新时间开始
6. **管理结果**: 查看测试状态，导出未测试目标，发送到扫描模块

### 🆕 测试状态管理
1. **查看测试状态**: 在测绘结果表格中查看每个目标的测试状态
2. **导出未测试目标**: 点击"导出未测试"按钮导出未测试的目标列表
3. **批量发送扫描**: 点击"发送未测试到扫描"将所有未测试目标添加到扫描列表
4. **自动状态更新**: 扫描完成后自动标记目标为已测试

## 远程服务器集成

本工具支持与远程服务器集成，实现扫描结果上传和特征同步功能：

1. 配置远程服务器地址：在"设置"页面填写API服务器地址（例如：`http://example.com/api/scan/results`）
2. 配置API密钥：填写有效的API密钥，用于身份验证
3. 可选配置客户端ID和名称：用于在服务器端标识不同的客户端
4. 从服务器同步挖矿特征：点击"从服务器同步"按钮获取最新的挖矿特征库
   - 同步过程会保留本地特征，避免覆盖本地自定义特征
   - 支持对本地特征进行上传、编辑和删除操作
5. 自动上传扫描结果：每次扫描完成后，结果会自动上传到配置的服务器

### 特征管理功能

1. **添加特征**：添加新特征后可选择是否立即上传到服务器
2. **编辑特征**：修改特征文本和匹配类型，支持同步更新到服务器
3. **上传特征**：将本地特征上传到远程服务器进行共享
4. **删除特征**：删除本地特征，如果已同步则同时从服务器删除
5. **同步状态显示**：直观显示每个特征的同步状态和远程ID

详细的服务器API文档请参考`api.md`文件。

## 数据存储

- 扫描结果保存在`db/redis_unauthorized.db`
- 配置信息保存在`db/config.db`

## 技术栈

- Electron
- Node.js
- SQLite3
- Redis客户端
- ExcelJS

## 许可证

MIT

## 故障排除

如果您在使用过程中遇到以下问题，可以尝试以下解决方案：

### 1. 数据库错误

- **错误信息**: `TypeError: rows is not iterable` 或 `SQLITE_BUSY: database is locked`
- **解决方案**: 这些问题已在最新版本中修复。如果仍然出现，可以尝试删除`db`目录下的数据库文件，然后重启应用程序。

### 2. 扫描超时

- **错误信息**: 扫描过程中卡住或无响应
- **解决方案**: 应用程序现在添加了超时机制，如果仍然出现此问题，可以尝试减少批量扫描的目标数量。

### 3. 挖矿特征匹配问题

- **错误信息**: 未能正确识别挖矿特征
- **解决方案**: 检查特征列表中的正则表达式是否有效，或者尝试使用精确匹配模式。

### 4. 远程服务器连接问题

- **错误信息**: `上传失败: 连接被拒绝`或`获取特征失败: 连接超时`
- **解决方案**: 确认远程服务器地址正确且可访问，检查API密钥是否有效，确保网络连接正常。

### 5. 特征同步问题

- **错误信息**: `特征同步失败` 或 `无法标记特征为已同步`
- **解决方案**: 确保远程服务器支持特征管理API，并且本地数据库结构已更新到最新版本。

如果遇到其他问题，请提交Issue或联系开发者。
