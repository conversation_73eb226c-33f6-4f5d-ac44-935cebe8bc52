服务数据深度查询接口
接口地址：/api/v3/scroll/quake_service
请求方式：POST
接口参数
字段	类型	必填	默认值	备注
pagination_id	str	否	无	分页id，指定分页id能够获取更多分页数据，分页id过期时间为5分钟
query	str	是	*	查询语句
rule	str	否	无	类型为IP列表的服务数据收藏名称
ip_list	List[str]	否	无	IP列表
size	int	否	10	单次分页大小，分页大小越大，请求时间越长
ignore_cache	bool	否	false	是否忽略缓存
start_time	str	否	无	查询起始时间，接受2020-10-14 00:00:00格式的数据，时区为UTC
end_time	str	否	无	查询截止时间，接受2020-10-14 00:00:00格式的数据，时区为UTC
include	List(str)	否	无	包含字段
exclude	List(str)	否	无	排除字段
latest	bool	否	false	是否使用最新数据
include 和 exclude参数，可传参字段从获取可筛选服务字段接口获取

使用方法
不指定pagination_id，发送第一个请求request 1
第一个请求的返回包response 1中，data字段为返回的查询数据，meta.pagination_id字段为该查询的分页id
第二次发送同样的请求，并指定pagination_id为第一次请求中的pagination_id（注意：除pagination_id外的其他查询参数不能更改）
第二次请求返回值包含第二页的查询数据
循环使用pagination_id发送更多请求，获取更多页的查询数据
当pagination_id不变且data列表为空时,则代表翻到最后一页
使用范例
第一次请求shell范例
curl -X POST "https://quake.360.net/api/v3/scroll/quake_service" -H "X-QuakeToken: d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx" -H "Content-Type: application/json" -d '{
    "query": "service: http",
    "size": 1,
    "ignore_cache": false,
    "start_time": "2021-01-07 00:13:14",
    "end_time": "2021-05-20 01:13:14"
}'
第一次请求python范例
import requests

headers = {
    "X-QuakeToken": "d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx",
    "Content-Type": "application/json"
}

data = {
     "query": '''service: http''',
     "start": 0,
     "size": 1,
     "ignore_cache": False,
     "latest":True,
     "shortcuts": [
        "610ce2adb1a2e3e1632e67b1"
    ],
}

response = requests.post(
    url="https://quake.360.net/api/v3/scroll/quake_service", headers=headers, json=data
)
print(response.json())
第一次返回值
{
  "code": 0,
  "message": "Successful.",
  "data": [
    // 返回数据
  ],
  "meta": {
    "total": {"value": 8078638343, "relation": "eq"},
    "pagination_id": "66c5937994f2d3e429523da4",
    }
}
第N次请求shell范例
curl -X GET "https://quake.360.net/api/v3/scroll/quake_service" -H "X-QuakeToken: d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx" -H "Content-Type: application/json" -d '{
    "query": "service: http",
    "pagination_id": "66c5937994f2d3e429523da4",
    "size": 1,
    "ignore_cache": false,
    "start_time": "2021-01-07 00:13:14",
    "end_time": "2021-05-20 01:13:14"
}' 
第N次请求python范例
import requests

headers = {
    "X-QuakeToken": "d17140ae-xxxx-xxx-xxxx-c0818b2bbxxx",
    "Content-Type": "application/json"
}

data = {
    "query": "service: http",
    "pagination_id": "66c5937994f2d3e429523da4",
    "size": 1,
    "ignore_cache": False,
    "start_time": "2021-01-07 00:13:14",
    "end_time": "2021-05-20 01:13:14",
}

response = requests.post(
    url="https://quake.360.net/api/v3/scroll/quake_service", headers=headers, json=data
)
print(response.json())
第N次返回值
{
  "code": 0,
  "message": "Successful.",
  "data": [
    // 返回数据
  ],
  "meta": {
    "total": {"value": 8078638343, "relation": "eq"},
    "pagination_id": "66c5937994f2d3e429523da4",
    }
}
获取聚合数据筛选字段接口
接口地址：/api/v3/aggregation/quake_service
请求方式：GET
使用范例
shell请求范例
curl -X G