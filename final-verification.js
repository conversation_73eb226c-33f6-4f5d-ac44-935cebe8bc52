const { initDb, getMappingStats, getUntestedMappingResults, markMappingResultsAsTested, saveMappingResult } = require('./src/database');
const { validateTimeRange, formatTimeForQuake, parseQuakeData } = require('./src/quake-mapper');

async function finalVerification() {
  console.log('🔍 最终功能验证');
  console.log('='.repeat(60));
  
  let allTestsPassed = true;
  const testResults = [];
  
  try {
    // 初始化数据库
    await initDb();
    console.log('✅ 数据库初始化成功');
    
    // 测试1: 数据库结构验证
    console.log('\n📊 测试1: 数据库结构验证');
    console.log('-'.repeat(40));
    
    try {
      const stats = await getMappingStats();
      console.log(`✅ 测绘统计功能正常 - 总数: ${stats.total_count}, 已测试: ${stats.tested_count}`);
      testResults.push({ test: '数据库结构', status: 'PASS' });
    } catch (error) {
      console.log(`❌ 测绘统计功能异常: ${error.message}`);
      testResults.push({ test: '数据库结构', status: 'FAIL', error: error.message });
      allTestsPassed = false;
    }
    
    // 测试2: 测试状态管理
    console.log('\n🎯 测试2: 测试状态管理');
    console.log('-'.repeat(40));
    
    try {
      // 创建测试数据
      const testData = {
        ip: '*************',
        port: 6379,
        hostname: 'test.example.com',
        transport: 'tcp',
        asn: 'AS4134',
        org: 'Test Organization',
        service_name: 'redis',
        country_cn: '中国',
        province_cn: '北京',
        city_cn: '北京',
        service_response: 'redis_version:6.2.0',
        time: new Date().toISOString(),
        mapping_type: 'redis_unauthorized',
        query_used: 'test_query',
        raw_data: JSON.stringify({ test: 'data' })
      };
      
      await saveMappingResult(testData);
      console.log('✅ 测试数据创建成功');
      
      // 获取未测试目标
      const untestedResults = await getUntestedMappingResults('redis_unauthorized', 1);
      if (untestedResults.length > 0) {
        console.log('✅ 未测试目标获取成功');
        
        // 标记为已测试
        const targets = [`${untestedResults[0].ip}:${untestedResults[0].port}`];
        const markedCount = await markMappingResultsAsTested(targets);
        
        if (markedCount > 0) {
          console.log('✅ 测试状态标记成功');
          testResults.push({ test: '测试状态管理', status: 'PASS' });
        } else {
          throw new Error('标记测试状态失败');
        }
      } else {
        console.log('⚠️  没有未测试目标，跳过标记测试');
        testResults.push({ test: '测试状态管理', status: 'SKIP' });
      }
    } catch (error) {
      console.log(`❌ 测试状态管理异常: ${error.message}`);
      testResults.push({ test: '测试状态管理', status: 'FAIL', error: error.message });
      allTestsPassed = false;
    }
    
    // 测试3: 时间范围验证
    console.log('\n⏰ 测试3: 时间范围验证');
    console.log('-'.repeat(40));
    
    try {
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
      
      // 测试有效范围
      const validRange = validateTimeRange(
        formatTimeForQuake(oneWeekAgo),
        formatTimeForQuake(now)
      );
      
      if (validRange.valid) {
        console.log('✅ 有效时间范围验证通过');
      } else {
        throw new Error(`有效时间范围验证失败: ${validRange.error}`);
      }
      
      // 测试无效范围
      const invalidRange = validateTimeRange(
        formatTimeForQuake(twoMonthsAgo),
        formatTimeForQuake(now)
      );
      
      if (!invalidRange.valid && invalidRange.error.includes('30天')) {
        console.log('✅ 无效时间范围验证通过');
        testResults.push({ test: '时间范围验证', status: 'PASS' });
      } else {
        throw new Error('无效时间范围验证失败');
      }
    } catch (error) {
      console.log(`❌ 时间范围验证异常: ${error.message}`);
      testResults.push({ test: '时间范围验证', status: 'FAIL', error: error.message });
      allTestsPassed = false;
    }
    
    // 测试4: 数据解析功能
    console.log('\n🔄 测试4: 数据解析功能');
    console.log('-'.repeat(40));
    
    try {
      const mockQuakeData = [{
        ip: '*************',
        port: 6379,
        hostname: 'test2.example.com',
        transport: 'tcp',
        asn: 'AS4134',
        org: 'Test Org 2',
        service: {
          name: 'redis',
          response: 'redis_version:7.0.0'
        },
        location: {
          country_cn: '中国',
          province_cn: '上海',
          city_cn: '上海'
        },
        time: '2024-01-01T12:00:00Z'
      }];
      
      const parsedData = parseQuakeData(mockQuakeData, 'redis_unauthorized', 'test_query');
      
      if (parsedData.length > 0 && parsedData[0].ip === '*************') {
        console.log('✅ 数据解析功能正常');
        testResults.push({ test: '数据解析功能', status: 'PASS' });
      } else {
        throw new Error('数据解析结果不正确');
      }
    } catch (error) {
      console.log(`❌ 数据解析功能异常: ${error.message}`);
      testResults.push({ test: '数据解析功能', status: 'FAIL', error: error.message });
      allTestsPassed = false;
    }
    
    // 测试5: 积分信息处理
    console.log('\n💰 测试5: 积分信息处理');
    console.log('-'.repeat(40));
    
    try {
      const mockUserData = {
        user: { username: 'test_user' },
        credit: 1000,
        persistent_credit: 500
      };
      
      // 模拟积分信息处理
      const totalCredit = mockUserData.credit + (mockUserData.persistent_credit || 0);
      
      if (totalCredit === 1500) {
        console.log('✅ 积分信息处理正常');
        console.log(`   普通积分: ${mockUserData.credit}`);
        console.log(`   长效积分: ${mockUserData.persistent_credit}`);
        console.log(`   总积分: ${totalCredit}`);
        testResults.push({ test: '积分信息处理', status: 'PASS' });
      } else {
        throw new Error('积分计算错误');
      }
    } catch (error) {
      console.log(`❌ 积分信息处理异常: ${error.message}`);
      testResults.push({ test: '积分信息处理', status: 'FAIL', error: error.message });
      allTestsPassed = false;
    }
    
    // 测试结果汇总
    console.log('\n📋 测试结果汇总');
    console.log('='.repeat(60));
    
    testResults.forEach((result, index) => {
      const status = result.status === 'PASS' ? '✅ 通过' : 
                    result.status === 'SKIP' ? '⚠️  跳过' : '❌ 失败';
      console.log(`${index + 1}. ${result.test}: ${status}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (allTestsPassed) {
      console.log('🎉 所有测试通过！新功能已准备就绪');
      console.log('\n📝 功能清单:');
      console.log('✅ 长效积分显示');
      console.log('✅ 测试状态跟踪');
      console.log('✅ 未测试目标管理');
      console.log('✅ 自定义时间范围');
      console.log('✅ 自动测绘功能');
      
      console.log('\n🚀 启动应用体验完整功能:');
      console.log('   npm start');
    } else {
      console.log('⚠️  部分测试失败，请检查相关功能');
      const failedTests = testResults.filter(r => r.status === 'FAIL');
      console.log(`失败的测试: ${failedTests.map(t => t.test).join(', ')}`);
    }
    
  } catch (error) {
    console.error('验证过程中出现严重错误:', error.message);
    allTestsPassed = false;
  }
  
  return allTestsPassed;
}

// 运行验证
if (require.main === module) {
  finalVerification().then((success) => {
    console.log('\n验证完成');
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('验证失败:', error);
    process.exit(1);
  });
}

module.exports = { finalVerification };
