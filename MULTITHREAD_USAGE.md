# 多线程扫描使用指南

## 🚀 快速开始

### 1. 启用多线程扫描
1. 启动Redis扫描工具
2. 点击"设置"标签页
3. 在"扫描性能设置"部分：
   - ✅ 勾选"启用多线程扫描"
   - 设置"线程数量"（建议4-8个）
4. 点击"保存设置"

### 2. 执行批量扫描
1. 切换到"扫描"标签页
2. 选择"批量扫描"模式
3. 输入多个目标（每行一个）或导入文件
4. 点击"开始扫描"

**注意**: 单个目标扫描会自动使用单线程模式

## 📊 性能对比

| 扫描方式 | 适用场景 | 性能特点 | 资源使用 |
|---------|---------|---------|---------|
| 传统异步 | 单个目标、小批量 | 稳定可靠 | 低CPU、低内存 |
| 多线程 | 大批量扫描 | 2-4倍提升 | 高CPU、中等内存 |

## ⚙️ 配置建议

### 线程数量设置
```
CPU核心数    建议线程数    适用场景
2-4核       2-4个        轻量级扫描
4-8核       4-6个        常规批量扫描  
8核以上     6-8个        大规模扫描
```

### 网络环境优化
- **局域网**: 可使用较多线程（6-8个）
- **公网**: 建议较少线程（2-4个），避免触发防护
- **高延迟网络**: 多线程优势更明显

## 🔧 技术实现

### 架构说明
```
用户界面 (渲染进程)
    ↓ IPC通信
主进程 (多线程扫描)
    ↓ 任务分发
Worker线程池 (并行扫描)
    ↓ 结果汇总
数据库存储
```

### 关键特性
- ✅ 自动错误恢复
- ✅ 智能任务调度
- ✅ 实时进度显示
- ✅ 资源自动清理
- ✅ 回退机制保障

## 📈 使用场景

### 适合多线程的场景
- 批量扫描（>10个目标）
- 网络延迟较高的环境
- CPU资源充足的系统
- 需要快速完成的任务

### 适合传统扫描的场景
- 单个目标扫描
- 系统资源有限
- 网络带宽受限
- 目标服务器负载敏感

## 🛠️ 故障排除

### 常见问题
1. **多线程不生效**
   - 检查是否启用多线程选项
   - 确认目标数量大于1
   - 查看控制台日志

2. **性能没有提升**
   - 调整线程数量
   - 检查网络环境
   - 监控系统资源

3. **扫描中断**
   - 系统会自动回退到传统扫描
   - 检查内存使用情况
   - 减少线程数量

### 调试方法
```bash
# 测试多线程功能
npm run test-multithread

# 性能对比测试
node performance-test.js
```

## 📋 最佳实践

### 1. 配置优化
- 根据CPU核心数设置线程数
- 监控系统资源使用情况
- 定期清理扫描结果

### 2. 扫描策略
- 大批量任务分批处理
- 避免同时扫描过多目标
- 合理设置扫描间隔

### 3. 错误处理
- 启用自动重试机制
- 设置合理的超时时间
- 监控扫描成功率

## 🔍 监控指标

### 性能指标
- 扫描速度：目标数/秒
- 成功率：成功扫描/总扫描
- 资源使用：CPU、内存占用
- 响应时间：平均扫描耗时

### 系统指标
- 线程池状态
- 任务队列长度
- 错误恢复次数
- 网络连接数

## 📞 技术支持

如需帮助，请提供：
1. 系统配置信息
2. 扫描目标数量
3. 错误日志截图
4. 网络环境描述

---

**提示**: 多线程扫描会显著提升大批量扫描的性能，但请根据实际环境合理配置线程数量，避免对目标系统造成过大压力。
