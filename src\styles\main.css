/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 30px;
}

header h1 {
  color: #2c3e50;
  font-size: 28px;
}

/* 按钮样式 */
button {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.primary-btn {
  background-color: #3498db;
  color: white;
}

.primary-btn:hover {
  background-color: #2980b9;
}

.secondary-btn {
  background-color: #2ecc71;
  color: white;
}

.secondary-btn:hover {
  background-color: #27ae60;
}

.danger-btn {
  background-color: #e74c3c;
  color: white;
}

.danger-btn:hover {
  background-color: #c0392b;
}

.small-btn {
  padding: 4px 8px;
  font-size: 12px;
  margin-left: 10px;
}

/* 标签页样式 */
.tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.tab-btn {
  background-color: transparent;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  color: #7f8c8d;
  border-bottom: 2px solid transparent;
}

.tab-btn.active {
  color: #3498db;
  border-bottom: 2px solid #3498db;
}

.tab-pane {
  display: none;
  padding: 20px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-pane.active {
  display: block;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 10px;
}

textarea {
  height: 150px;
  resize: vertical;
}

.file-input {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.file-input button {
  margin-right: 10px;
}

.file-input span {
  color: #7f8c8d;
  font-size: 14px;
}

/* 进度条样式 */
.scan-progress {
  margin-top: 30px;
}

.progress-bar {
  height: 20px;
  background-color: #ecf0f1;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #3498db;
  width: 0;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #7f8c8d;
}

/* 结果表格样式 */
.results-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.results-filter select {
  width: 150px;
}

.results-actions button {
  margin-left: 10px;
}

/* 复选框样式 */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

#select-all-results {
  margin: 0;
}

/* 上传状态样式 */
.upload-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.upload-status.uploaded {
  background-color: #d4efdf;
  color: #27ae60;
}

.upload-status.not-uploaded {
  background-color: #f8f9f9;
  color: #95a5a6;
}

/* 同步状态样式 */
.sync-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.sync-status.synced {
  background-color: #d6eaf8;
  color: #3498db;
}

.sync-status.not-synced {
  background-color: #f8f9f9;
  color: #95a5a6;
}

/* 开关样式 */
.switch-container {
  margin-top: 15px;
  display: flex;
  align-items: center;
}

.switch-container label {
  margin-right: 10px;
  margin-bottom: 0;
}

.switch-container input[type="checkbox"] {
  margin-right: 10px;
}

.switch-description {
  font-size: 12px;
  color: #7f8c8d;
  margin-left: 10px;
}

.results-table-container {
  overflow-x: auto;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th,
.results-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.results-table th {
  background-color: #f9f9f9;
  font-weight: bold;
  color: #2c3e50;
}

.results-table tr:hover {
  background-color: #f5f5f5;
}

.results-table .success {
  color: #2ecc71;
}

.results-table .fail {
  color: #e74c3c;
}

.results-table .ransomware {
  color: #e67e22;
  font-weight: bold;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.page-btn {
  background-color: #ecf0f1;
  color: #7f8c8d;
  margin: 0 10px;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#page-info {
  font-size: 14px;
  color: #7f8c8d;
}

/* 特征列表样式 */
.feature-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.feature-text {
  font-size: 14px;
  margin-bottom: 4px;
  word-break: break-all;
}

.feature-details {
  display: flex;
  gap: 10px;
}

.feature-type {
  font-size: 12px;
  color: #7f8c8d;
}

.feature-type.regex {
  color: #e67e22;
}

.feature-type.exact {
  color: #2ecc71;
}

.feature-sync-status {
  font-size: 12px;
  font-style: italic;
}

.feature-sync-status.synced {
  color: #2ecc71;
}

.feature-sync-status.unsynced {
  color: #7f8c8d;
}

.feature-actions {
  display: flex;
  gap: 8px;
}

.feature-delete,
.feature-edit,
.feature-upload {
  cursor: pointer;
  font-weight: bold;
  padding: 2px 5px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.feature-delete {
  color: #e74c3c;
}

.feature-delete:hover {
  background-color: #fadbd8;
}

.feature-edit {
  color: #3498db;
}

.feature-edit:hover {
  background-color: #d6eaf8;
}

.feature-upload {
  color: #27ae60;
}

.feature-upload:hover {
  background-color: #d4efdf;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.feature-header span {
  font-weight: bold;
  color: #2c3e50;
}

.feature-add {
  display: flex;
  margin-bottom: 10px;
}

.feature-add input {
  flex-grow: 1;
  margin-right: 10px;
  margin-bottom: 0;
}

.feature-add select {
  width: 120px;
  margin-right: 10px;
  margin-bottom: 0;
}

.match-type-info {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 15px;
}

.match-type-info p {
  margin: 5px 0;
}

.match-type-info code {
  background-color: #eee;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.feature-edit-form .form-group {
  margin-bottom: 15px;
}

.feature-edit-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #2c3e50;
}

.feature-edit-form input,
.feature-edit-form select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group h3 {
  margin-top: 20px;
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

/* 按钮样式 */
.small-btn.secondary-btn {
  background-color: #27ae60;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
}

.small-btn.secondary-btn:hover {
  background-color: #219d54;
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  position: relative;
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  width: 80%;
  max-width: 800px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  color: #2c3e50;
  font-size: 20px;
}

.close-modal {
  font-size: 24px;
  color: #7f8c8d;
  cursor: pointer;
}

.close-modal:hover {
  color: #2c3e50;
}

.redis-info {
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
  color: #2c3e50;
  margin-right: 10px;
}

.key-value-container {
  max-height: 400px;
  overflow-y: auto;
}

.key-value-container h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.db-section {
  margin-bottom: 20px;
}

.db-header {
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-weight: bold;
}

.key-item {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.key-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.key-type {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.key-value {
  word-break: break-all;
  font-family: monospace;
  background-color: #f9f9f9;
  padding: 5px;
  border-radius: 4px;
  max-height: 150px;
  overflow-y: auto;
}

/* 扫描结果摘要样式 */
.scan-result-summary {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

.result-item {
  margin: 10px 0;
  font-size: 16px;
}

.result-label {
  font-weight: bold;
  margin-right: 10px;
  color: #2c3e50;
}

.result-value {
  font-size: 18px;
  font-weight: bold;
}

.result-value.success {
  color: #2ecc71;
}

.result-value.fail {
  color: #e74c3c;
}

.result-value.ransomware {
  color: #e67e22;
}

/* 对话框按钮区域 */
.dialog-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-buttons button {
  margin-left: 10px;
}

/* 对话框内容区域 */
#dialog-content {
  margin-bottom: 15px;
  line-height: 1.5;
}

/* 对话框进度条区域 */
#dialog-progress {
  margin: 20px 0;
}

.input-hint {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: -5px;
  margin-bottom: 15px;
}

/* 结果计数样式 */
.results-count {
  margin-left: 20px;
  color: #7f8c8d;
  font-size: 14px;
  display: inline-block;
  padding: 5px 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

/* 加载消息样式 */
.loading-message {
  text-align: center;
  padding: 20px;
  color: #3498db;
  font-style: italic;
}

/* 错误消息样式 */
.error-message {
  text-align: center;
  padding: 20px;
  color: #e74c3c;
  background-color: #fadbd8;
  border-radius: 4px;
}

/* 测绘页面样式 */
.mapping-container {
  padding: 20px;
}

.mapping-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.mapping-controls .form-group {
  margin-bottom: 15px;
}

.mapping-controls label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.mapping-controls input,
.mapping-controls select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.mapping-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.mapping-status {
  background: #e9ecef;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-info {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.status-info span {
  font-weight: 500;
}

.mapping-results {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
}

.results-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.results-stats span {
  font-weight: 500;
}

.mapping-table {
  width: 100%;
  border-collapse: collapse;
}

.mapping-table th,
.mapping-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.mapping-table th {
  background: #f8f9fa;
  font-weight: 600;
}

.mapping-table tbody tr:hover {
  background: #f8f9fa;
}

.no-data {
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* 测试状态样式 */
.status-tested {
  color: #28a745;
  font-weight: 500;
}

.status-untested {
  color: #dc3545;
  font-weight: 500;
}

.status-tested + small {
  color: #6c757d;
  font-size: 11px;
}

/* 时间输入框样式 */
input[type="datetime-local"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* 按钮组样式优化 */
.mapping-actions .btn {
  white-space: nowrap;
  min-width: 100px;
}

.mapping-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}