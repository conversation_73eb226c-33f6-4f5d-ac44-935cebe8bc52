# Quake测绘模块功能更新说明

## 🚀 新增功能概览

### 1. 长效积分显示
- **功能**: 在用户信息中显示Quake账户的长效积分（persistent_credit）
- **位置**: 测绘页面状态栏和Token验证对话框
- **作用**: 帮助用户了解完整的积分情况，合理规划测绘任务

### 2. 测试状态跟踪
- **功能**: 记录和显示测绘结果的测试状态
- **特性**:
  - 自动标记已扫描的目标为"已测试"
  - 显示测试时间
  - 区分已测试和未测试目标
  - 提供测试状态统计

### 3. 未测试目标管理
- **导出未测试目标**: 一键导出所有未测试的测绘目标
- **发送到扫描模块**: 批量将未测试目标添加到扫描列表
- **智能筛选**: 只处理有效的IP:端口组合

### 4. 自定义时间范围测绘
- **功能**: 支持指定时间范围进行测绘
- **参数**:
  - 开始时间（可选）
  - 结束时间（可选）
- **验证**: 自动验证时间范围有效性（不超过30天）

### 5. 自动测绘功能
- **智能续传**: 从最新测绘时间开始，避免重复数据
- **首次测绘**: 如果没有历史数据，获取所有可用数据
- **增量更新**: 只获取新增的数据，提高效率

## 📊 界面改进

### 用户状态显示
```
用户: username  积分: 1000  长效积分: 500
```

### 测绘结果统计
```
总数: 1000  有效: 995  已测试: 200  未测试: 795  最新: 2024-01-01 12:00:00
```

### 测绘结果表格
| IP地址 | 端口 | 位置 | 组织 | 服务 | 时间 | 测试状态 | 操作 |
|--------|------|------|------|------|------|----------|------|
| ************* | 6379 | 中国 北京 | China Tel... | redis | 2024-01-01 | **已测试**<br><small>2024-01-01 14:30</small> | 添加到扫描 |
| ********* | 6379 | 中国 上海 | China Uni... | redis | 2024-01-01 | **未测试** | 添加到扫描 |

### 新增按钮
- **自动测绘**: 智能增量测绘
- **导出未测试**: 导出未测试目标列表
- **发送未测试到扫描**: 批量添加未测试目标到扫描

## 🔧 技术实现

### 数据库扩展
```sql
-- 测绘结果表新增字段
ALTER TABLE mapping_results ADD COLUMN is_tested INTEGER DEFAULT 0;
ALTER TABLE mapping_results ADD COLUMN tested_time TEXT;

-- 测绘任务表新增字段
ALTER TABLE mapping_tasks ADD COLUMN time_range_start TEXT;
ALTER TABLE mapping_tasks ADD COLUMN time_range_end TEXT;
ALTER TABLE mapping_tasks ADD COLUMN auto_mapping INTEGER DEFAULT 0;
```

### 核心函数
- `markMappingResultAsTested()` - 标记单个目标为已测试
- `markMappingResultsAsTested()` - 批量标记目标为已测试
- `getUntestedMappingResults()` - 获取未测试目标
- `getMappingStats()` - 获取测绘统计信息
- `executeAutoMapping()` - 执行自动测绘
- `validateTimeRange()` - 验证时间范围

### API增强
- Quake API调用支持时间范围参数
- 用户信息API返回长效积分
- 自动测绘逻辑优化

## 📋 使用指南

### 1. 查看积分信息
1. 输入Quake API Token
2. 点击"验证Token"
3. 查看用户信息中的积分和长效积分

### 2. 自定义时间范围测绘
1. 在测绘配置中设置开始时间和结束时间
2. 留空表示不限制该时间边界
3. 系统会自动验证时间范围有效性
4. 点击"开始测绘"执行

### 3. 自动测绘
1. 点击"自动测绘"按钮
2. 系统自动从最新数据时间开始测绘
3. 避免重复获取已有数据
4. 适合定期更新数据使用

### 4. 管理未测试目标
1. 查看测绘结果中的测试状态列
2. 使用"导出未测试"导出目标列表
3. 使用"发送未测试到扫描"批量添加到扫描
4. 扫描完成后自动标记为已测试

### 5. 测试状态跟踪
- 扫描完成后，目标自动标记为已测试
- 在测绘结果表格中查看测试状态
- 绿色表示已测试，红色表示未测试
- 显示具体的测试时间

## 🎯 使用场景

### 场景1: 定期安全检查
1. 使用自动测绘获取最新目标
2. 批量发送未测试目标到扫描
3. 执行安全扫描
4. 查看测试覆盖率

### 场景2: 历史数据分析
1. 设置时间范围获取特定时期数据
2. 分析不同时间段的Redis分布
3. 对比测试结果变化

### 场景3: 增量测试
1. 首次全量测绘
2. 定期自动测绘获取新目标
3. 只测试新发现的目标
4. 提高测试效率

## ⚠️ 注意事项

### 积分管理
- 长效积分通常有使用限制
- 合理规划测绘频率和数量
- 监控积分消耗情况

### 时间范围限制
- 单次查询时间范围不超过30天
- 过大的时间范围可能导致超时
- 建议分批查询大时间跨度数据

### 自动测绘
- 首次使用建议先手动测绘
- 确保有足够积分进行自动测绘
- 定期检查自动测绘结果

### 测试状态
- 只有通过本工具扫描的目标才会标记为已测试
- 外部扫描不会更新测试状态
- 可以手动清空测绘数据重新开始

## 🔮 后续规划

1. **测试报告生成**: 自动生成测试覆盖率报告
2. **定时任务**: 支持定时自动测绘
3. **多引擎支持**: 集成更多测绘引擎
4. **智能推荐**: 基于历史数据推荐测绘策略
5. **数据可视化**: 测绘结果地理分布图

---

**版本**: v2.1.0  
**更新时间**: 2024年1月  
**兼容性**: 向后兼容，自动升级数据库结构
