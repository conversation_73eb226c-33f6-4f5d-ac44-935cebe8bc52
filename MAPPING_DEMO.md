# Quake测绘模块演示指南

## 功能演示流程

### 1. 启动应用
```bash
npm start
```

### 2. 配置Quake Token

#### 步骤一：获取Token
1. 访问 https://quake.360.net
2. 注册/登录账户
3. 在用户中心获取API Token

#### 步骤二：配置Token
**方法一：在测绘页面**
1. 点击"测绘"标签页
2. 在"Quake API Token"输入框输入Token
3. 点击"验证Token"按钮

**方法二：在设置页面**
1. 点击"设置"标签页
2. 在"测绘引擎配置"部分输入Token
3. 点击"保存设置"

### 3. 执行测绘任务

#### 境内Redis未授权测绘
1. 选择测绘模式："境内Redis未授权"
2. 设置最大结果数：1000（建议值）
3. 设置批次大小：100（建议值）
4. 点击"开始测绘"

#### 境内Redis挖矿测绘
1. 选择测绘模式："境内Redis挖矿"
2. 设置最大结果数：500（挖矿目标相对较少）
3. 设置批次大小：100
4. 点击"开始测绘"

### 4. 监控测绘进度
- 观察用户信息和积分显示
- 查看实时进度条
- 监控数据获取统计

### 5. 管理测绘结果
- 查看测绘结果表格
- 使用分页浏览数据
- 查看详细的地理位置和组织信息

### 6. 集成扫描功能
1. 点击结果表格中的"添加到扫描"按钮
2. 自动切换到扫描页面
3. 目标自动添加到批量扫描列表
4. 执行Redis安全扫描

## 界面截图说明

### 测绘页面主界面
```
┌─────────────────────────────────────────────────────────────┐
│ Quake测绘引擎                                                │
│ 使用Quake测绘引擎获取Redis服务目标，为扫描模块提供数据源      │
├─────────────────────────────────────────────────────────────┤
│ Quake API Token: [********************] [验证Token]         │
│ 测绘模式: [境内Redis未授权 ▼]                               │
│ 最大结果数: [1000]  批次大小: [100]                         │
│ [开始测绘] [停止测绘] [清空结果] [导出目标]                  │
├─────────────────────────────────────────────────────────────┤
│ 用户: username  积分: 1000                                  │
│ ████████████████████████████████████████ 100%              │
│ 已获取 1000/1000 条数据                                     │
└─────────────────────────────────────────────────────────────┘
```

### 测绘结果表格
```
┌─────────────────────────────────────────────────────────────┐
│ 测绘结果                                                    │
│ 总数: 1000  有效: 995  最新: 2024-01-01 12:00:00           │
├─────────────────────────────────────────────────────────────┤
│ IP地址        │端口│位置      │组织        │服务  │操作      │
├─────────────────────────────────────────────────────────────┤
│ ************* │6379│中国 北京 │China Tel...│redis │添加到扫描│
│ *********     │6379│中国 上海 │China Uni...│redis │添加到扫描│
│ ************  │6379│中国 广东 │China Mob...│redis │添加到扫描│
└─────────────────────────────────────────────────────────────┘
│ [上一页] 第 1 页，共 1000 条 [下一页]                       │
└─────────────────────────────────────────────────────────────┘
```

## 实际使用案例

### 案例1：安全评估项目
**场景**: 对某地区Redis服务进行安全评估
**步骤**:
1. 使用"境内Redis未授权"模式获取目标
2. 设置地理位置过滤（通过查询语句）
3. 导出目标列表
4. 批量执行安全扫描
5. 生成安全评估报告

### 案例2：挖矿威胁检测
**场景**: 检测网络中的Redis挖矿威胁
**步骤**:
1. 使用"境内Redis挖矿"模式
2. 获取疑似挖矿的Redis服务
3. 逐个添加到扫描列表
4. 执行详细的安全分析
5. 确认挖矿行为并处置

### 案例3：资产发现
**场景**: 发现组织内部的Redis资产
**步骤**:
1. 配置组织相关的查询条件
2. 执行测绘获取资产列表
3. 导出资产清单
4. 进行资产管理和安全加固

## 性能数据

### 测绘速度
- **小批量** (100条): 约10-30秒
- **中批量** (1000条): 约1-3分钟
- **大批量** (5000条): 约5-15分钟

### 积分消耗
- **实时查询**: 每100条约消耗10积分
- **深度查询**: 每100条约消耗5积分
- **建议**: 单次查询不超过5000条

### 数据质量
- **有效率**: 通常95%以上
- **去重率**: 自动去重，无重复数据
- **时效性**: 数据通常为近期采集

## 常见问题解答

### Q1: Token验证失败怎么办？
**A**: 
1. 检查Token是否正确复制
2. 确认Quake账户状态正常
3. 验证网络连接是否正常
4. 检查是否有足够的积分

### Q2: 测绘结果为空怎么办？
**A**:
1. 检查查询条件是否过于严格
2. 尝试调整时间范围
3. 确认目标地区确实有相关服务
4. 检查积分是否充足

### Q3: 如何优化测绘效率？
**A**:
1. 合理设置批次大小（建议100-200）
2. 避免单次查询过多数据
3. 在网络良好时执行测绘
4. 定期清理过期数据

### Q4: 数据如何与扫描模块集成？
**A**:
1. 使用"添加到扫描"功能
2. 或导出目标列表手动导入
3. 支持批量添加多个目标
4. 自动切换到批量扫描模式

## 技术支持

### 日志查看
- 打开开发者工具（F12）
- 查看Console标签页
- 观察API请求和响应

### 数据备份
- 定期导出重要的测绘结果
- 保存为文本文件或Excel格式
- 建议按项目或时间分类存储

### 性能监控
- 监控系统内存使用
- 观察网络请求状态
- 检查数据库存储空间

---

**注意**: 请确保在授权范围内使用测绘功能，遵守相关法律法规，仅用于合法的安全研究和评估目的。
