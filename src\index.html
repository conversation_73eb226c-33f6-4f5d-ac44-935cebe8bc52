<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redis未授权访问检测工具</title>
  <link rel="stylesheet" href="./styles/main.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="container">
    <header>
      <h1>Redis未授权访问检测工具</h1>
    </header>

    <div class="tabs">
      <button class="tab-btn active" data-tab="scan">扫描</button>
      <button class="tab-btn" data-tab="mapping">测绘</button>
      <button class="tab-btn" data-tab="dashboard">仪表盘</button>
      <button class="tab-btn" data-tab="results">结果</button>
      <button class="tab-btn" data-tab="settings">设置</button>
    </div>

    <div class="tab-content">
      <!-- 扫描页面 -->
      <div class="tab-pane active" id="scan">
        <div class="scan-form">
          <div class="form-group">
            <label for="scan-type">扫描类型</label>
            <select id="scan-type">
              <option value="single">单个扫描</option>
              <option value="batch">批量扫描</option>
            </select>
          </div>

          <div class="form-group" id="single-input">
            <label for="redis-host">Redis主机地址</label>
            <input type="text" id="redis-host" placeholder="127.0.0.1">
            <label for="redis-port">端口</label>
            <input type="number" id="redis-port" placeholder="6379" value="6379">
          </div>

          <div class="form-group" id="batch-input" style="display: none;">
            <label for="redis-targets">批量目标（每行一个，格式：地址:端口）</label>
            <textarea id="redis-targets" placeholder="127.0.0.1:6379&#10;*************:6379"></textarea>
            <div class="file-input">
              <button id="import-file">导入文件</button>
              <span id="file-name">未选择文件</span>
            </div>
          </div>

          <button id="start-scan" class="primary-btn">开始扫描</button>
        </div>
      </div>

      <!-- 测绘页面 -->
      <div class="tab-pane" id="mapping">
        <div class="mapping-container">
          <div class="mapping-header">
            <h2>Quake测绘引擎</h2>
            <p>使用Quake测绘引擎获取Redis服务目标，为扫描模块提供数据源</p>
          </div>

          <div class="mapping-config">
            <div class="form-group">
              <label for="quake-token">Quake API Token:</label>
              <input type="password" id="quake-token" placeholder="请输入您的Quake API Token">
              <button id="check-token-btn" class="btn btn-secondary">验证Token</button>
            </div>

            <div class="form-group">
              <label for="mapping-mode">测绘模式:</label>
              <select id="mapping-mode">
                <option value="redis_unauthorized">境内Redis未授权</option>
                <option value="redis_mining">境内Redis挖矿</option>
              </select>
            </div>

            <div class="form-group">
              <label for="max-results">最大结果数:</label>
              <input type="number" id="max-results" value="1000" min="100" max="10000" step="100">
              <span class="input-hint">建议设置1000-5000，过多可能消耗大量积分</span>
            </div>

            <div class="form-group">
              <label for="batch-size">批次大小:</label>
              <input type="number" id="batch-size" value="100" min="50" max="500" step="50">
              <span class="input-hint">每次请求的数据量，建议100-200</span>
            </div>

            <div class="form-group">
              <label for="start-time">开始时间 (可选):</label>
              <input type="datetime-local" id="start-time">
              <span class="input-hint">留空获取所有历史数据</span>
            </div>

            <div class="form-group">
              <label for="end-time">结束时间 (可选):</label>
              <input type="datetime-local" id="end-time">
              <span class="input-hint">留空获取到当前时间的数据</span>
            </div>
          </div>

          <div class="mapping-actions">
            <button id="start-mapping-btn" class="btn btn-primary">开始测绘</button>
            <button id="auto-mapping-btn" class="btn btn-primary">自动测绘</button>
            <button id="stop-mapping-btn" class="btn btn-danger" disabled>停止测绘</button>
            <button id="clear-mapping-btn" class="btn btn-secondary">清空结果</button>
            <button id="export-targets-btn" class="btn btn-success">导出目标</button>
            <button id="export-untested-btn" class="btn btn-success">导出未测试</button>
            <button id="send-untested-btn" class="btn btn-primary">发送未测试到扫描</button>
          </div>

          <div class="mapping-status">
            <div class="status-info">
              <span id="user-info">未登录</span>
              <span id="credit-info">积分: --</span>
              <span id="persistent-credit-info">长效积分: --</span>
            </div>
            <div class="progress-container" style="display: none;">
              <div class="progress-bar">
                <div class="progress-fill" id="mapping-progress-fill"></div>
              </div>
              <div class="progress-text" id="mapping-progress-text">准备中...</div>
            </div>
          </div>

          <div class="mapping-results">
            <div class="results-header">
              <h3>测绘结果</h3>
              <div class="results-stats">
                <span id="mapping-total-count">总数: 0</span>
                <span id="mapping-success-count">有效: 0</span>
                <span id="mapping-tested-count">已测试: 0</span>
                <span id="mapping-untested-count">未测试: 0</span>
                <span id="mapping-latest-time">最新: --</span>
              </div>
            </div>

            <div class="results-table-container">
              <table class="results-table" id="mapping-results-table">
                <thead>
                  <tr>
                    <th>IP地址</th>
                    <th>端口</th>
                    <th>位置</th>
                    <th>组织</th>
                    <th>服务</th>
                    <th>时间</th>
                    <th>测试状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="mapping-results-tbody">
                  <tr>
                    <td colspan="8" class="no-data">暂无测绘数据</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div class="results-pagination">
              <button id="mapping-prev-btn" class="btn btn-secondary" disabled>上一页</button>
              <span id="mapping-page-info">第 1 页，共 0 条</span>
              <button id="mapping-next-btn" class="btn btn-secondary" disabled>下一页</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 仪表盘页面 -->
      <div class="tab-pane" id="dashboard">
        <div class="dashboard-container">
          <div class="dashboard-header">
            <h2>数据统计仪表盘</h2>
            <div class="dashboard-actions">
              <button id="refresh-dashboard" class="btn btn-primary">刷新数据</button>
              <button id="export-dashboard" class="btn btn-secondary">导出报告</button>
            </div>
          </div>

          <!-- 概览卡片 -->
          <div class="overview-cards">
            <div class="overview-card">
              <div class="card-icon scan-icon">📊</div>
              <div class="card-content">
                <div class="card-title">扫描总数</div>
                <div class="card-value" id="total-scans">0</div>
                <div class="card-subtitle">累计扫描目标</div>
              </div>
            </div>

            <div class="overview-card success">
              <div class="card-icon">✅</div>
              <div class="card-content">
                <div class="card-title">成功连接</div>
                <div class="card-value" id="success-scans">0</div>
                <div class="card-subtitle">可访问Redis</div>
              </div>
            </div>

            <div class="overview-card danger">
              <div class="card-icon">⚠️</div>
              <div class="card-content">
                <div class="card-title">挖矿检测</div>
                <div class="card-value" id="ransomware-scans">0</div>
                <div class="card-subtitle">发现挖矿活动</div>
              </div>
            </div>

            <div class="overview-card info">
              <div class="card-icon">🗺️</div>
              <div class="card-content">
                <div class="card-title">测绘数据</div>
                <div class="card-value" id="mapping-total">0</div>
                <div class="card-subtitle">测绘目标总数</div>
              </div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="charts-container">
            <!-- 第一行图表 -->
            <div class="chart-row">
              <div class="chart-card">
                <div class="chart-header">
                  <h3>扫描结果分布</h3>
                  <div class="chart-controls">
                    <select id="scan-chart-type">
                      <option value="pie">饼图</option>
                      <option value="doughnut">环形图</option>
                    </select>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="scan-results-chart"></canvas>
                </div>
              </div>

              <div class="chart-card">
                <div class="chart-header">
                  <h3>省份分布统计</h3>
                  <div class="chart-controls">
                    <select id="province-data-source">
                      <option value="mapping">测绘数据</option>
                      <option value="scan">扫描数据</option>
                    </select>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="province-chart"></canvas>
                </div>
              </div>
            </div>

            <!-- 第二行图表 -->
            <div class="chart-row">
              <div class="chart-card">
                <div class="chart-header">
                  <h3>时间趋势分析</h3>
                  <div class="chart-controls">
                    <select id="time-range">
                      <option value="7">最近7天</option>
                      <option value="30">最近30天</option>
                      <option value="90">最近90天</option>
                    </select>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="time-trend-chart"></canvas>
                </div>
              </div>

              <div class="chart-card">
                <div class="chart-header">
                  <h3>组织分布TOP10</h3>
                  <div class="chart-controls">
                    <button id="toggle-org-chart" class="btn btn-sm">切换视图</button>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="org-chart"></canvas>
                </div>
              </div>
            </div>

            <!-- 第三行图表 -->
            <div class="chart-row">
              <div class="chart-card full-width">
                <div class="chart-header">
                  <h3>端口分布统计</h3>
                  <div class="chart-controls">
                    <select id="port-chart-limit">
                      <option value="10">TOP 10</option>
                      <option value="20">TOP 20</option>
                      <option value="50">TOP 50</option>
                    </select>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="port-chart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- 详细统计表格 -->
          <div class="stats-tables">
            <div class="stats-table-card">
              <div class="table-header">
                <h3>省份详细统计</h3>
                <button id="export-province-stats" class="btn btn-sm btn-secondary">导出</button>
              </div>
              <div class="table-container">
                <table class="stats-table" id="province-stats-table">
                  <thead>
                    <tr>
                      <th>省份</th>
                      <th>总数</th>
                      <th>成功</th>
                      <th>挖矿</th>
                      <th>成功率</th>
                      <th>挖矿率</th>
                    </tr>
                  </thead>
                  <tbody id="province-stats-tbody">
                    <tr>
                      <td colspan="6" class="no-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="stats-table-card">
              <div class="table-header">
                <h3>组织详细统计</h3>
                <button id="export-org-stats" class="btn btn-sm btn-secondary">导出</button>
              </div>
              <div class="table-container">
                <table class="stats-table" id="org-stats-table">
                  <thead>
                    <tr>
                      <th>组织</th>
                      <th>总数</th>
                      <th>成功</th>
                      <th>挖矿</th>
                      <th>成功率</th>
                      <th>挖矿率</th>
                    </tr>
                  </thead>
                  <tbody id="org-stats-tbody">
                    <tr>
                      <td colspan="6" class="no-data">暂无数据</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结果页面 -->
      <div class="tab-pane" id="results">
        <div class="results-header">
          <div class="results-filter">
            <label for="result-filter">筛选结果:</label>
            <select id="result-filter">
              <option value="all">全部</option>
              <option value="success">成功</option>
              <option value="fail">失败</option>
              <option value="ransomware">挖矿</option>
              <option value="synced">已同步</option>
              <option value="unsynced">未同步</option>
            </select>
          </div>
          <div class="results-actions">
            <button id="sync-results" class="primary-btn">同步结果</button>
            <button id="upload-results" class="primary-btn">上传结果</button>
            <button id="export-results" class="secondary-btn">导出Excel</button>
            <button id="clear-results" class="danger-btn">清空结果</button>
          </div>
        </div>

        <div class="results-table-container">
          <table class="results-table">
            <thead>
              <tr>
                <th><input type="checkbox" id="select-all-results"></th>
                <th>ID</th>
                <th>主机</th>
                <th>端口</th>
                <th>状态</th>
                <th>连接时间</th>
                <th>状态信息</th>
                <th>上传状态</th>
                <th>同步状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="results-body">
              <!-- 结果将在这里动态加载 -->
            </tbody>
          </table>
        </div>

        <div class="pagination">
          <button id="prev-page" class="page-btn">上一页</button>
          <span id="page-info">第 1 页，共 1 页</span>
          <button id="next-page" class="page-btn">下一页</button>
        </div>
      </div>

      <!-- 设置页面 -->
      <div class="tab-pane" id="settings">
        <div class="settings-form">
          <div class="form-group">
            <h3>远程服务器设置</h3>
            <label for="api-server">API服务器地址:</label>
            <input type="text" id="api-server" placeholder="如: https://example.com 或 http://example.com:3000">
            <div class="input-hint">必须以 http:// 或 https:// 开头，请勿在末尾添加 /api/scan/results 等路径</div>

            <label for="api-key">API密钥:</label>
            <input type="text" id="api-key" placeholder="服务器API密钥">
            <label for="client-id">客户端ID (可选):</label>
            <input type="text" id="client-id" placeholder="客户端唯一标识符">
            <label for="client-name">客户端名称 (可选):</label>
            <input type="text" id="client-name" placeholder="客户端名称">

            <div class="switch-container">
              <label for="auto-upload">自动上传扫描结果:</label>
              <input type="checkbox" id="auto-upload">
              <span class="switch-description">启用后，每次扫描完成将自动上传结果到服务器</span>
            </div>
          </div>

          <div class="form-group">
            <h3>扫描性能设置</h3>
            <div class="switch-container">
              <label for="use-multi-thread">启用多线程扫描:</label>
              <input type="checkbox" id="use-multi-thread">
              <span class="switch-description">启用后，批量扫描将使用多线程提高扫描速度</span>
            </div>

            <label for="thread-count">线程数量:</label>
            <input type="number" id="thread-count" min="1" max="16" placeholder="8">
            <div class="input-hint">建议设置为CPU核心数，最多16个线程。单个目标扫描不使用多线程。</div>
          </div>

          <div class="form-group">
            <h3>测绘引擎配置</h3>
            <label for="quake-token-setting">Quake API Token:</label>
            <input type="password" id="quake-token-setting" placeholder="请输入您的Quake API Token">
            <div class="input-hint">用于Quake测绘引擎，获取Redis服务目标数据。请到 <a href="https://quake.360.net" target="_blank">Quake官网</a> 获取Token</div>
          </div>

          <div class="form-group">
            <h3>挖矿特征管理</h3>
            <div class="feature-header">
              <span>特征列表</span>
              <button id="sync-features" class="secondary-btn small-btn">从服务器同步</button>
            </div>
            <div class="feature-list" id="feature-list">
              <!-- 特征列表将在这里动态加载 -->
            </div>
            <div class="feature-add">
              <input type="text" id="new-feature" placeholder="添加新特征...">
              <select id="match-type">
                <option value="exact">精确匹配</option>
                <option value="regex">正则匹配</option>
              </select>
              <button id="add-feature" class="secondary-btn">添加</button>
            </div>
            <div class="match-type-info">
              <p><strong>匹配类型说明：</strong></p>
              <p><strong>精确匹配：</strong> 查找键值数据中是否包含指定的文本（不区分大小写）</p>
              <p><strong>正则匹配：</strong> 使用正则表达式进行匹配，如 <code>\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b</code> 匹配邮箱</p>
            </div>
          </div>

          <button id="save-settings" class="primary-btn">保存设置</button>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal" id="details-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Redis详情</h2>
          <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
          <div class="redis-info">
            <div class="info-item">
              <span class="info-label">主机地址:</span>
              <span class="info-value" id="detail-host"></span>
            </div>
            <div class="info-item">
              <span class="info-label">端口:</span>
              <span class="info-value" id="detail-port"></span>
            </div>
            <div class="info-item">
              <span class="info-label">连接状态:</span>
              <span class="info-value" id="detail-status"></span>
            </div>
            <div class="info-item">
              <span class="info-label">连接时间:</span>
              <span class="info-value" id="detail-time"></span>
            </div>
            <div class="info-item">
              <span class="info-label">状态信息:</span>
              <span class="info-value" id="detail-info"></span>
            </div>
            <div class="info-item">
              <span class="info-label">是否挖矿:</span>
              <span class="info-value" id="detail-ransomware"></span>
              <button id="mark-ransomware" class="small-btn">标记为挖矿</button>
              <button id="unmark-ransomware" class="small-btn">取消标记</button>
            </div>
          </div>

          <div class="key-value-container">
            <h3>键值数据</h3>
            <div id="key-value-data">
              <!-- 键值数据将在这里动态加载 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通用对话框组件 -->
    <div class="modal" id="common-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="dialog-title">提示</h2>
          <span class="close-modal" id="dialog-close">&times;</span>
        </div>
        <div class="modal-body">
          <div id="dialog-content">
            <!-- 对话框内容将在这里动态加载 -->
          </div>

          <!-- 进度条部分 -->
          <div id="dialog-progress" style="display: none;">
            <div class="progress-bar">
              <div class="progress-fill" id="dialog-progress-fill"></div>
            </div>
            <div class="progress-text" id="dialog-progress-text">扫描中... 0/0</div>
          </div>

          <!-- 结果部分 -->
          <div id="dialog-result" style="display: none;">
            <div class="scan-result-summary">
              <div class="result-item">
                <span class="result-label">总数:</span>
                <span class="result-value" id="result-total">0</span>
              </div>
              <div class="result-item">
                <span class="result-label">成功:</span>
                <span class="result-value success" id="result-success">0</span>
              </div>
              <div class="result-item">
                <span class="result-label">失败:</span>
                <span class="result-value fail" id="result-fail">0</span>
              </div>
              <div class="result-item">
                <span class="result-label">挖矿:</span>
                <span class="result-value ransomware" id="result-ransomware">0</span>
              </div>
            </div>
          </div>

          <div class="dialog-buttons" id="dialog-buttons">
            <!-- 按钮将在这里动态加载 -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="./renderer.js"></script>
</body>
</html>