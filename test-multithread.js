const { batchScanRedisMultiThread } = require('./src/redis-scanner');
const { initDb } = require('./src/database');

async function testMultiThreadScan() {
  console.log('开始测试多线程Redis扫描...');
  
  try {
    // 初始化数据库
    await initDb();
    console.log('数据库初始化完成');
    
    // 测试目标列表
    const targets = [
      '127.0.0.1:6379',
      '127.0.0.1:6380',
      '***********:6379',
      '***********:6379',
      '********:6379'
    ];
    
    console.log(`测试目标: ${targets.join(', ')}`);
    
    // 进度回调函数
    const onProgress = (completed, total) => {
      const percent = ((completed / total) * 100).toFixed(1);
      console.log(`进度: ${completed}/${total} (${percent}%)`);
    };
    
    // 单个扫描完成回调
    const onScanComplete = (result) => {
      console.log(`扫描完成: ${result.host}:${result.port} - ${result.status === 1 ? '成功' : '失败'} - ${result.status_info}`);
    };
    
    // 执行多线程扫描
    const startTime = Date.now();
    const results = await batchScanRedisMultiThread(targets, {
      maxThreads: 4,
      onProgress,
      onScanComplete
    });
    const endTime = Date.now();
    
    console.log('\n=== 扫描结果 ===');
    console.log(`总耗时: ${endTime - startTime}ms`);
    console.log(`扫描目标数: ${targets.length}`);
    console.log(`返回结果数: ${results.length}`);
    
    const successCount = results.filter(r => r.status === 1).length;
    const failCount = results.filter(r => r.status === 0).length;
    const ransomwareCount = results.filter(r => r.is_ransomware === 1).length;
    
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`检测到勒索: ${ransomwareCount}`);
    
    console.log('\n=== 详细结果 ===');
    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.host}:${result.port}`);
      console.log(`   状态: ${result.status === 1 ? '成功' : '失败'}`);
      console.log(`   信息: ${result.status_info}`);
      console.log(`   勒索: ${result.is_ransomware === 1 ? '是' : '否'}`);
      console.log(`   键值数量: ${Object.keys(result.key_values).length}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testMultiThreadScan().then(() => {
    console.log('测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testMultiThreadScan };
