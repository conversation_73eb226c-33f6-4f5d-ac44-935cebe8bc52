const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { formatDateForMySQL } = require('./utils');

// 确保db目录存在
const dbDir = path.join(__dirname, '..', 'db');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// 数据库路径
const scanDbPath = path.join(dbDir, 'redis_unauthorized.db');
const configDbPath = path.join(dbDir, 'config.db');

// 初始化数据库
async function initDb() {
  try {
    await initScanDb();
    await initConfigDb();
  } catch (err) {
    console.error('数据库初始化失败:', err.message);
    // 继续运行程序，允许用户使用其他功能
  }
}

// 初始化扫描结果数据库
function initScanDb() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      // 设置超时和忙等待超时
      db.run("PRAGMA busy_timeout = 5000;", (err) => {
        if (err) {
          console.error('设置busy_timeout失败:', err.message);
          // 继续执行，不中断流程
        }

        // 创建扫描结果表
        db.run(`CREATE TABLE IF NOT EXISTS scan_results (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          host TEXT NOT NULL,
          port INTEGER NOT NULL,
          status INTEGER NOT NULL,
          connect_time TEXT NOT NULL,
          status_info TEXT,
          key_values TEXT,
          is_ransomware INTEGER DEFAULT 0,
          auto_detected INTEGER DEFAULT 0,
          is_uploaded INTEGER DEFAULT 0,
          uploaded_at TEXT,
          is_synced INTEGER DEFAULT 0,
          remote_id INTEGER DEFAULT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
          if (err) {
            console.error('创建扫描结果表失败:', err.message);
            db.close();
            reject(err);
            return;
          }

          // 检查是否需要添加同步相关的字段
          db.all("PRAGMA table_info(scan_results)", (err, rows) => {
            if (err) {
              console.error('获取表信息失败:', err.message);
              db.close();
              reject(err);
              return;
            }

            // 检查字段是否存在
            let isSyncedExists = false;
            let remoteIdExists = false;
            let isUploadedExists = false;
            let uploadedAtExists = false;

            if (rows && Array.isArray(rows)) {
              for (const row of rows) {
                if (row.name === 'is_synced') {
                  isSyncedExists = true;
                }
                if (row.name === 'remote_id') {
                  remoteIdExists = true;
                }
                if (row.name === 'is_uploaded') {
                  isUploadedExists = true;
                }
                if (row.name === 'uploaded_at') {
                  uploadedAtExists = true;
                }
              }
            }

            // 添加缺失的字段
            db.serialize(() => {
              // 如果is_synced字段不存在，添加它
              if (!isSyncedExists) {
                db.run(`ALTER TABLE scan_results ADD COLUMN is_synced INTEGER DEFAULT 0`, (err) => {
                  if (err) {
                    console.error('添加is_synced字段失败:', err.message);
                  } else {
                    console.log('成功添加is_synced字段到scan_results表');
                  }
                });
              }

              // 如果remote_id字段不存在，添加它
              if (!remoteIdExists) {
                db.run(`ALTER TABLE scan_results ADD COLUMN remote_id INTEGER DEFAULT NULL`, (err) => {
                  if (err) {
                    console.error('添加remote_id字段失败:', err.message);
                  } else {
                    console.log('成功添加remote_id字段到scan_results表');
                  }
                });
              }

              // 如果is_uploaded字段不存在，添加它
              if (!isUploadedExists) {
                db.run(`ALTER TABLE scan_results ADD COLUMN is_uploaded INTEGER DEFAULT 0`, (err) => {
                  if (err) {
                    console.error('添加is_uploaded字段失败:', err.message);
                  } else {
                    console.log('成功添加is_uploaded字段到scan_results表');
                  }
                });
              }

              // 如果uploaded_at字段不存在，添加它
              if (!uploadedAtExists) {
                db.run(`ALTER TABLE scan_results ADD COLUMN uploaded_at TEXT`, (err) => {
                  if (err) {
                    console.error('添加uploaded_at字段失败:', err.message);
                  } else {
                    console.log('成功添加uploaded_at字段到scan_results表');
                  }
                });
              }

              // 继续创建勒索特征表
              db.run(`CREATE TABLE IF NOT EXISTS ransomware_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feature_text TEXT NOT NULL,
                match_type TEXT DEFAULT 'exact', -- 'exact'表示完全匹配, 'regex'表示正则匹配
                remote_id INTEGER DEFAULT NULL, -- 远程服务器上的ID，NULL表示本地特征
                is_synced INTEGER DEFAULT 0, -- 1表示已同步到远程服务器，0表示本地特征
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
              )`, (err) => {
                if (err) {
                  console.error('创建勒索特征表失败:', err.message);
                  db.close();
                  reject(err);
                  return;
                }

                // 检查是否需要升级现有表以添加match_type字段
                db.all("PRAGMA table_info(ransomware_features)", (err, rows) => {
                  if (err) {
                    console.error('获取表信息失败:', err.message);
                    db.close();
                    reject(err);
                    return;
                  }

                  // 检查字段是否存在
                  let matchTypeExists = false;
                  let featureRemoteIdExists = false;
                  let featureIsSyncedExists = false;

                  if (rows && Array.isArray(rows)) {
                    for (const row of rows) {
                      if (row.name === 'match_type') {
                        matchTypeExists = true;
                      }
                      if (row.name === 'remote_id') {
                        featureRemoteIdExists = true;
                      }
                      if (row.name === 'is_synced') {
                        featureIsSyncedExists = true;
                      }
                    }
                  }

                  // 逐步添加缺失的字段
                  db.serialize(() => {
                    // 如果match_type字段不存在，添加它
                    if (!matchTypeExists) {
                      db.run(`ALTER TABLE ransomware_features ADD COLUMN match_type TEXT DEFAULT 'exact'`, (err) => {
                        if (err) {
                          console.error('添加match_type字段失败:', err.message);
                        } else {
                          console.log('成功添加match_type字段到ransomware_features表');
                        }
                      });
                    }

                    // 如果remote_id字段不存在，添加它
                    if (!featureRemoteIdExists) {
                      db.run(`ALTER TABLE ransomware_features ADD COLUMN remote_id INTEGER DEFAULT NULL`, (err) => {
                        if (err) {
                          console.error('添加remote_id字段失败:', err.message);
                        } else {
                          console.log('成功添加remote_id字段到ransomware_features表');
                        }
                      });
                    }

                    // 如果is_synced字段不存在，添加它
                    if (!featureIsSyncedExists) {
                      db.run(`ALTER TABLE ransomware_features ADD COLUMN is_synced INTEGER DEFAULT 0`, (err) => {
                        if (err) {
                          console.error('添加is_synced字段失败:', err.message);
                        } else {
                          console.log('成功添加is_synced字段到ransomware_features表');
                        }

                        // 创建测绘结果表
                        db.run(`CREATE TABLE IF NOT EXISTS mapping_results (
                          id INTEGER PRIMARY KEY AUTOINCREMENT,
                          ip TEXT NOT NULL,
                          port INTEGER NOT NULL,
                          hostname TEXT,
                          transport TEXT,
                          asn TEXT,
                          org TEXT,
                          service_name TEXT,
                          country_cn TEXT,
                          province_cn TEXT,
                          city_cn TEXT,
                          service_response TEXT,
                          service_cert TEXT,
                          time TEXT,
                          mapping_type TEXT NOT NULL,
                          query_used TEXT NOT NULL,
                          raw_data TEXT,
                          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                          UNIQUE(ip, port, mapping_type)
                        )`, (err) => {
                          if (err) {
                            console.error('创建测绘结果表失败:', err.message);
                          } else {
                            console.log('测绘结果表创建成功');
                          }

                          // 创建测绘任务表
                          db.run(`CREATE TABLE IF NOT EXISTS mapping_tasks (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            task_name TEXT NOT NULL,
                            mapping_type TEXT NOT NULL,
                            query_statement TEXT NOT NULL,
                            total_count INTEGER DEFAULT 0,
                            fetched_count INTEGER DEFAULT 0,
                            status TEXT DEFAULT 'pending',
                            pagination_id TEXT,
                            start_time TEXT,
                            end_time TEXT,
                            error_message TEXT,
                            time_range_start TEXT,
                            time_range_end TEXT,
                            auto_mapping INTEGER DEFAULT 0,
                            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                          )`, (err) => {
                            if (err) {
                              console.error('创建测绘任务表失败:', err.message);
                            } else {
                              console.log('测绘任务表创建成功');
                            }

                            // 升级测绘结果表，添加测试状态字段
                            db.run(`ALTER TABLE mapping_results ADD COLUMN is_tested INTEGER DEFAULT 0`, (err) => {
                              if (err && !err.message.includes('duplicate column name')) {
                                console.error('添加is_tested字段失败:', err.message);
                              } else if (!err) {
                                console.log('成功添加is_tested字段到mapping_results表');
                              }

                              db.run(`ALTER TABLE mapping_results ADD COLUMN tested_time TEXT`, (err) => {
                                if (err && !err.message.includes('duplicate column name')) {
                                  console.error('添加tested_time字段失败:', err.message);
                                } else if (!err) {
                                  console.log('成功添加tested_time字段到mapping_results表');
                                }

                                // 升级测绘任务表，添加时间范围和自动测绘字段
                                db.run(`ALTER TABLE mapping_tasks ADD COLUMN time_range_start TEXT`, (err) => {
                                  if (err && !err.message.includes('duplicate column name')) {
                                    console.error('添加time_range_start字段失败:', err.message);
                                  } else if (!err) {
                                    console.log('成功添加time_range_start字段到mapping_tasks表');
                                  }

                                  db.run(`ALTER TABLE mapping_tasks ADD COLUMN time_range_end TEXT`, (err) => {
                                    if (err && !err.message.includes('duplicate column name')) {
                                      console.error('添加time_range_end字段失败:', err.message);
                                    } else if (!err) {
                                      console.log('成功添加time_range_end字段到mapping_tasks表');
                                    }

                                    db.run(`ALTER TABLE mapping_tasks ADD COLUMN auto_mapping INTEGER DEFAULT 0`, (err) => {
                                      if (err && !err.message.includes('duplicate column name')) {
                                        console.error('添加auto_mapping字段失败:', err.message);
                                      } else if (!err) {
                                        console.log('成功添加auto_mapping字段到mapping_tasks表');
                                      }

                                      db.close();
                                      resolve();
                                    });
                                  });
                                });
                              });
                            });
                          });
                        });
                      });
                    } else {
                      // 创建测绘结果表
                      db.run(`CREATE TABLE IF NOT EXISTS mapping_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ip TEXT NOT NULL,
                        port INTEGER NOT NULL,
                        hostname TEXT,
                        transport TEXT,
                        asn TEXT,
                        org TEXT,
                        service_name TEXT,
                        country_cn TEXT,
                        province_cn TEXT,
                        city_cn TEXT,
                        service_response TEXT,
                        service_cert TEXT,
                        time TEXT,
                        mapping_type TEXT NOT NULL,
                        query_used TEXT NOT NULL,
                        raw_data TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(ip, port, mapping_type)
                      )`, (err) => {
                        if (err) {
                          console.error('创建测绘结果表失败:', err.message);
                        } else {
                          console.log('测绘结果表创建成功');
                        }

                        // 创建测绘任务表
                        db.run(`CREATE TABLE IF NOT EXISTS mapping_tasks (
                          id INTEGER PRIMARY KEY AUTOINCREMENT,
                          task_name TEXT NOT NULL,
                          mapping_type TEXT NOT NULL,
                          query_statement TEXT NOT NULL,
                          total_count INTEGER DEFAULT 0,
                          fetched_count INTEGER DEFAULT 0,
                          status TEXT DEFAULT 'pending',
                          pagination_id TEXT,
                          start_time TEXT,
                          end_time TEXT,
                          error_message TEXT,
                          time_range_start TEXT,
                          time_range_end TEXT,
                          auto_mapping INTEGER DEFAULT 0,
                          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                        )`, (err) => {
                          if (err) {
                            console.error('创建测绘任务表失败:', err.message);
                          } else {
                            console.log('测绘任务表创建成功');
                          }

                          // 升级测绘结果表，添加测试状态字段
                          db.run(`ALTER TABLE mapping_results ADD COLUMN is_tested INTEGER DEFAULT 0`, (err) => {
                            if (err && !err.message.includes('duplicate column name')) {
                              console.error('添加is_tested字段失败:', err.message);
                            } else if (!err) {
                              console.log('成功添加is_tested字段到mapping_results表');
                            }

                            db.run(`ALTER TABLE mapping_results ADD COLUMN tested_time TEXT`, (err) => {
                              if (err && !err.message.includes('duplicate column name')) {
                                console.error('添加tested_time字段失败:', err.message);
                              } else if (!err) {
                                console.log('成功添加tested_time字段到mapping_results表');
                              }

                              db.close();
                              resolve();
                            });
                          });
                        });
                      });
                    }
                  });
                });
              });
            });
          });
        });
      });
    });
  });
}

// 初始化配置数据库
function initConfigDb() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(configDbPath, (err) => {
      if (err) {
        console.error('配置数据库连接失败:', err.message);
        reject(err);
        return;
      }

      // 设置超时和忙等待超时
      db.run("PRAGMA busy_timeout = 5000;", (err) => {
        if (err) {
          console.error('设置busy_timeout失败:', err.message);
          // 继续执行，不中断流程
        }

        // 创建配置表
        db.run(`CREATE TABLE IF NOT EXISTS configs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
          if (err) {
            console.error('创建配置表失败:', err.message);
            db.close();
            reject(err);
            return;
          }

          // 插入默认配置
          const defaultConfigs = [
            { key: 'api_server', value: '' },
            { key: 'api_key', value: 'redis_scanner_api_key_2025' },
            { key: 'client_id', value: '' },
            { key: 'client_name', value: '' }
          ];

          // 使用事务批量插入
          db.serialize(() => {
            db.run('BEGIN TRANSACTION');

            const stmt = db.prepare('INSERT OR IGNORE INTO configs (key, value) VALUES (?, ?)');

            for (const config of defaultConfigs) {
              stmt.run(config.key, config.value);
            }

            stmt.finalize();

            db.run('COMMIT', (err) => {
              db.close();

              if (err) {
                console.error('插入默认配置失败:', err.message);
                reject(err);
                return;
              }

              resolve();
            });
          });
        });
      });
    });
  });
}

// 保存扫描结果（带去重）
function saveScanResult(result) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const { host, port, status, connect_time, status_info, key_values, is_ransomware, auto_detected } = result;

      // 先检查是否已存在相同host:port的记录
      db.get(
        `SELECT id FROM scan_results WHERE host = ? AND port = ?`,
        [host, port],
        (err, row) => {
          if (err) {
            console.error('查询记录失败:', err.message);
            db.close();
            reject(err);
            return;
          }

          if (row) {
            // 存在记录，更新
            db.run(
              `UPDATE scan_results SET
              status = ?,
              connect_time = ?,
              status_info = ?,
              key_values = ?,
              is_ransomware = ?,
              auto_detected = ?,
              is_uploaded = 0,
              uploaded_at = NULL,
              created_at = CURRENT_TIMESTAMP
              WHERE id = ?`,
              [
                status,
                connect_time,
                status_info,
                JSON.stringify(key_values),
                is_ransomware || 0,
                auto_detected || 0,
                row.id
              ],
              function(err) {
                db.close();
                if (err) {
                  console.error('更新记录失败:', err.message);
                  reject(err);
                  return;
                }
                resolve(row.id);
              }
            );
          } else {
            // 不存在记录，插入新记录
            db.run(
              `INSERT INTO scan_results
              (host, port, status, connect_time, status_info, key_values, is_ransomware, auto_detected, is_uploaded)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0)`,
              [
                host,
                port,
                status,
                connect_time,
                status_info,
                JSON.stringify(key_values),
                is_ransomware || 0,
                auto_detected || 0
              ],
              function(err) {
                db.close();
                if (err) {
                  console.error('保存记录失败:', err.message);
                  reject(err);
                  return;
                }
                resolve(this.lastID);
              }
            );
          }
        }
      );
    });
  });
}

// 获取扫描结果
function getScanResults(status) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = 'SELECT * FROM scan_results';
      if (status !== undefined) {
        query += ' WHERE status = ?';
      }
      query += ' ORDER BY created_at DESC';

      db.all(query, status !== undefined ? [status] : [], (err, rows) => {
        db.close();

        if (err) {
          console.error('获取扫描结果失败:', err.message);
          reject(err);
          return;
        }

        // 解析JSON字符串
        const results = rows.map(row => {
          try {
            row.key_values = JSON.parse(row.key_values || '{}');
          } catch (e) {
            row.key_values = {};
          }
          return row;
        });

        resolve(results);
      });
    });
  });
}

// 更新勒索标记
function updateRansomwareStatus(id, isRansomware) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run(`UPDATE scan_results SET is_ransomware = ? WHERE id = ?`,
        [isRansomware ? 1 : 0, id], function(err) {
          db.close();

          if (err) {
            console.error('更新勒索标记失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.changes);
        });
    });
  });
}

// 保存勒索特征
function saveRansomwareFeature(featureText, matchType = 'exact', remoteId = null, isSynced = 0) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      // 验证匹配类型
      if (matchType !== 'exact' && matchType !== 'regex') {
        matchType = 'exact'; // 默认为精确匹配
      }

      // 如果是正则类型，验证正则表达式是否有效
      if (matchType === 'regex') {
        try {
          new RegExp(featureText);
        } catch (e) {
          db.close();
          reject(new Error('无效的正则表达式: ' + e.message));
          return;
        }
      }

      db.run(`INSERT INTO ransomware_features (feature_text, match_type, remote_id, is_synced) VALUES (?, ?, ?, ?)`,
        [featureText, matchType, remoteId, isSynced], function(err) {
          db.close();

          if (err) {
            console.error('保存勒索特征失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.lastID);
        });
    });
  });
}

// 更新勒索特征
function updateRansomwareFeature(id, featureText, matchType, remoteId = null, isSynced = 0) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      // 验证匹配类型
      if (matchType !== 'exact' && matchType !== 'regex') {
        matchType = 'exact'; // 默认为精确匹配
      }

      // 如果是正则类型，验证正则表达式是否有效
      if (matchType === 'regex') {
        try {
          new RegExp(featureText);
        } catch (e) {
          db.close();
          reject(new Error('无效的正则表达式: ' + e.message));
          return;
        }
      }

      db.run(`UPDATE ransomware_features SET
              feature_text = ?,
              match_type = ?,
              remote_id = ?,
              is_synced = ?
              WHERE id = ?`,
        [featureText, matchType, remoteId, isSynced, id], function(err) {
          db.close();

          if (err) {
            console.error('更新勒索特征失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.changes);
        });
    });
  });
}

// 获取所有勒索特征
function getRansomwareFeatures() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.all(`SELECT * FROM ransomware_features`, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取勒索特征失败:', err.message);
          reject(err);
          return;
        }

        resolve(rows);
      });
    });
  });
}

// 获取配置
function getConfig(key) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(configDbPath, (err) => {
      if (err) {
        console.error('配置数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.get(`SELECT value FROM configs WHERE key = ?`, [key], (err, row) => {
        db.close();

        if (err) {
          console.error('获取配置失败:', err.message);
          reject(err);
          return;
        }

        resolve(row ? row.value : null);
      });
    });
  });
}

// 更新配置
function updateConfig(key, value) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(configDbPath, (err) => {
      if (err) {
        console.error('配置数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run(`INSERT OR REPLACE INTO configs (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)`,
        [key, value], function(err) {
          db.close();

          if (err) {
            console.error('更新配置失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.changes);
        });
    });
  });
}

// 清空扫描结果
function clearScanResults() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run('DELETE FROM scan_results', function(err) {
        db.close();

        if (err) {
          console.error('清空扫描结果失败:', err.message);
          reject(err);
          return;
        }

        resolve(this.changes);
      });
    });
  });
}

// 去重扫描结果（保留每个host:port的最新记录）
function deduplicateScanResults() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      // 使用更安全的方式执行去重操作
      db.serialize(() => {
        // 先删除临时表（如果存在）
        db.run("DROP TABLE IF EXISTS temp_latest_records", (err) => {
          if (err) {
            console.error('删除临时表失败:', err.message);
            db.close();
            reject(err);
            return;
          }

          // 创建临时表存储每个host:port组合的最新记录ID
          db.run(`
            CREATE TEMPORARY TABLE temp_latest_records AS
            SELECT MAX(id) as max_id
            FROM scan_results
            GROUP BY host, port
          `, (err) => {
            if (err) {
              console.error('创建临时表失败:', err.message);
              db.close();
              reject(err);
              return;
            }

            // 删除不在最新记录中的记录
            db.run(`
              DELETE FROM scan_results
              WHERE id NOT IN (SELECT max_id FROM temp_latest_records)
            `, function(err) {
              // 删除临时表
              db.run("DROP TABLE IF EXISTS temp_latest_records", (dropErr) => {
                if (dropErr) {
                  console.error('删除临时表失败:', dropErr.message);
                  // 继续处理，不中断流程
                }

                db.close();

                if (err) {
                  console.error('去重失败:', err.message);
                  reject(err);
                  return;
                }

                resolve(this.changes);
              });
            });
          });
        });
      });
    });
  });
}

// 批量保存扫描结果（去重后）
async function saveBatchScanResults(results) {
  // 先逐个保存结果
  for (const result of results) {
    await saveScanResult(result);
  }

  // 然后执行去重操作
  return await deduplicateScanResults();
}

// 删除勒索特征
function deleteRansomwareFeature(id) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run(`DELETE FROM ransomware_features WHERE id = ?`, [id], function(err) {
        db.close();

        if (err) {
          console.error('删除挖矿特征失败:', err.message);
          reject(err);
          return;
        }

        resolve(this.changes);
      });
    });
  });
}

// 标记特征为已同步
function markFeatureAsSynced(id, remoteId) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run(`UPDATE ransomware_features SET
              remote_id = ?,
              is_synced = 1
              WHERE id = ?`,
        [remoteId, id], function(err) {
          db.close();

          if (err) {
            console.error('标记特征同步状态失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.changes);
        });
    });
  });
}

// 从远程服务器同步挖矿特征（保留本地特征）
async function syncFeaturesFromRemote(features) {
  try {
    // 获取所有本地特征
    const localFeatures = await getRansomwareFeatures();

    // 创建两个映射：一个用于远程ID到特征的映射，一个用于特征文本到特征的映射
    const remoteIdMap = new Map();
    const textMap = new Map();

    // 先处理本地特征
    for (const feature of localFeatures) {
      if (feature.remote_id) {
        remoteIdMap.set(feature.remote_id, feature);
      }
      // 创建特征文本和匹配类型的组合键
      const textTypeKey = `${feature.feature_text}:${feature.match_type}`;
      textMap.set(textTypeKey, feature);
    }

    // 处理远程特征
    for (const remoteFeature of features) {
      const remoteId = remoteFeature.id;
      const featureText = remoteFeature.feature_text;
      const matchType = remoteFeature.match_type || 'exact';
      const textTypeKey = `${featureText}:${matchType}`;

      // 检查是否已存在相同远程ID的特征
      if (remoteIdMap.has(remoteId)) {
        // 更新现有特征
        const existingFeature = remoteIdMap.get(remoteId);
        await updateRansomwareFeature(
          existingFeature.id,
          featureText,
          matchType,
          remoteId,
          1 // 标记为已同步
        );
      }
      // 检查是否存在相同文本和匹配类型的特征
      else if (textMap.has(textTypeKey)) {
        // 更新现有特征，添加远程ID
        const existingFeature = textMap.get(textTypeKey);
        await updateRansomwareFeature(
          existingFeature.id,
          featureText,
          matchType,
          remoteId,
          1 // 标记为已同步
        );
      }
      // 否则，添加新特征
      else {
        await saveRansomwareFeature(
          featureText,
          matchType,
          remoteId,
          1 // 标记为已同步
        );
      }
    }

    return true;
  } catch (err) {
    console.error('同步特征失败:', err.message);
    return false;
  }
}

// 获取未同步的特征
function getUnsyncedFeatures() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.all(`SELECT * FROM ransomware_features WHERE is_synced = 0`, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取未同步特征失败:', err.message);
          reject(err);
          return;
        }

        resolve(rows || []);
      });
    });
  });
}

// 清空挖矿特征
function clearRansomwareFeatures() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.run('DELETE FROM ransomware_features', function(err) {
        db.close();

        if (err) {
          console.error('清空挖矿特征失败:', err.message);
          reject(err);
          return;
        }

        resolve(this.changes);
      });
    });
  });
}

// 获取未上传的扫描结果
function getUnuploadedResults() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.all(`SELECT * FROM scan_results WHERE is_uploaded = 0 ORDER BY created_at DESC`, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取未上传结果失败:', err.message);
          reject(err);
          return;
        }

        // 解析JSON字符串
        const results = rows.map(row => {
          try {
            row.key_values = JSON.parse(row.key_values || '{}');
          } catch (e) {
            console.warn(`解析结果ID ${row.id} 的key_values失败:`, e.message);
            row.key_values = {};
          }
          return row;
        });

        console.log(`获取到 ${results.length} 条未上传的结果`);
        resolve(results);
      });
    });
  });
}

// 标记扫描结果为已上传
function markResultsAsUploaded(ids) {
  return new Promise((resolve, reject) => {
    if (!ids || ids.length === 0) {
      resolve(0);
      return;
    }

    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const placeholders = ids.map(() => '?').join(',');
      // 使用formatDateForMySQL函数格式化日期
      const currentTime = formatDateForMySQL(new Date());

      db.run(
        `UPDATE scan_results SET is_uploaded = 1, uploaded_at = ? WHERE id IN (${placeholders})`,
        [currentTime, ...ids],
        function(err) {
          db.close();

          if (err) {
            console.error('标记结果为已上传失败:', err.message);
            reject(err);
            return;
          }

          resolve(this.changes);
        }
      );
    });
  });
}

// 同步远程扫描结果到本地数据库（保留本地的额外数据）
async function syncScanResultsFromRemote(remoteResults) {
  try {
    if (!remoteResults || !Array.isArray(remoteResults) || remoteResults.length === 0) {
      return {
        success: false,
        message: '无远程扫描结果可同步',
        syncedCount: 0,
        totalCount: 0
      };
    }

    let syncedCount = 0;
    const totalCount = remoteResults.length;

    // 获取本地所有结果，用于比较
    const localResults = await getScanResults();

    // 创建本地结果的映射，以host:port为键
    const localResultsMap = new Map();
    for (const result of localResults) {
      const key = `${result.host}:${result.port}`;
      localResultsMap.set(key, result);
    }

    // 逐个处理远程结果
    for (const remoteResult of remoteResults) {
      const key = `${remoteResult.host}:${remoteResult.port}`;
      const localResult = localResultsMap.get(key);

      try {
        // 处理远程结果的key_values字段
        let remoteKeyValues = remoteResult.key_values;

        // 如果key_values是字符串，尝试解析成对象
        if (typeof remoteKeyValues === 'string') {
          try {
            remoteKeyValues = JSON.parse(remoteKeyValues);
          } catch (e) {
            console.warn(`无法解析远程结果ID ${remoteResult.id} 的key_values:`, e.message);
            remoteKeyValues = {};
          }
        }
        // 如果key_values不是对象，设置为空对象
        else if (!remoteKeyValues || typeof remoteKeyValues !== 'object') {
          console.warn(`远程结果ID ${remoteResult.id} 的key_values不是对象类型:`, typeof remoteKeyValues);
          remoteKeyValues = {};
        }

        if (localResult) {
          // 如果本地已有该结果，保留本地的额外数据，合并远程数据

          // 获取本地键值数据
          let localKeyValues = localResult.key_values;
          if (typeof localKeyValues === 'string') {
            try {
              localKeyValues = JSON.parse(localKeyValues);
            } catch (e) {
              localKeyValues = {};
            }
          } else if (!localKeyValues || typeof localKeyValues !== 'object') {
            localKeyValues = {};
          }

          // 合并键值数据 - 优先使用远程数据，因为它可能更新
          const mergedKeyValues = { ...localKeyValues, ...remoteKeyValues };

          // 合并更新本地结果
          await updateScanResult({
            id: localResult.id,
            host: remoteResult.host,
            port: remoteResult.port,
            status: remoteResult.status,
            connect_time: remoteResult.connect_time,
            status_info: remoteResult.status_info,
            key_values: JSON.stringify(mergedKeyValues),
            is_ransomware: remoteResult.is_ransomware,
            auto_detected: remoteResult.auto_detected,
            is_synced: 1,  // 标记为已同步
            remote_id: remoteResult.id,  // 保存远程ID
            is_uploaded: 1,  // 标记为已上传
            uploaded_at: formatDateForMySQL(new Date())
          });
        } else {
          // 如果本地没有该结果，直接添加
          await saveScanResultWithSync({
            host: remoteResult.host,
            port: remoteResult.port,
            status: remoteResult.status,
            connect_time: remoteResult.connect_time,
            status_info: remoteResult.status_info,
            key_values: remoteKeyValues,
            is_ransomware: remoteResult.is_ransomware,
            auto_detected: remoteResult.auto_detected,
            is_synced: 1,  // 标记为已同步
            remote_id: remoteResult.id,  // 保存远程ID
            is_uploaded: 1,  // 标记为已上传
            uploaded_at: formatDateForMySQL(new Date())
          });
        }

        syncedCount++;
      } catch (err) {
        console.error(`同步结果 ${key} 失败:`, err.message);
        // 继续处理下一个结果
      }
    }

    return {
      success: true,
      message: `成功同步 ${syncedCount}/${totalCount} 个扫描结果`,
      syncedCount,
      totalCount
    };
  } catch (err) {
    console.error('同步扫描结果失败:', err.message);
    return {
      success: false,
      message: `同步扫描结果失败: ${err.message}`,
      syncedCount: 0,
      totalCount: remoteResults.length
    };
  }
}

// 保存扫描结果（带同步标记）
function saveScanResultWithSync(result) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const {
        host, port, status, connect_time, status_info, key_values,
        is_ransomware, auto_detected, is_synced, remote_id, is_uploaded, uploaded_at
      } = result;

      // 先检查是否已存在相同host:port的记录
      db.get(
        `SELECT id FROM scan_results WHERE host = ? AND port = ?`,
        [host, port],
        (err, row) => {
          if (err) {
            console.error('查询记录失败:', err.message);
            db.close();
            reject(err);
            return;
          }

          if (row) {
            // 存在记录，更新
            db.run(
              `UPDATE scan_results SET
              status = ?,
              connect_time = ?,
              status_info = ?,
              key_values = ?,
              is_ransomware = ?,
              auto_detected = ?,
              is_synced = ?,
              remote_id = ?,
              is_uploaded = ?,
              uploaded_at = ?,
              created_at = CURRENT_TIMESTAMP
              WHERE id = ?`,
              [
                status,
                connect_time,
                status_info,
                JSON.stringify(key_values),
                is_ransomware || 0,
                auto_detected || 0,
                is_synced || 0,
                remote_id || null,
                is_uploaded || 0,
                uploaded_at || null,
                row.id
              ],
              function(err) {
                db.close();
                if (err) {
                  console.error('更新记录失败:', err.message);
                  reject(err);
                  return;
                }
                resolve(row.id);
              }
            );
          } else {
            // 不存在记录，插入新记录
            db.run(
              `INSERT INTO scan_results
              (host, port, status, connect_time, status_info, key_values, is_ransomware, auto_detected, is_synced, remote_id, is_uploaded, uploaded_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                host,
                port,
                status,
                connect_time,
                status_info,
                JSON.stringify(key_values),
                is_ransomware || 0,
                auto_detected || 0,
                is_synced || 0,
                remote_id || null,
                is_uploaded || 0,
                uploaded_at || null
              ],
              function(err) {
                db.close();
                if (err) {
                  console.error('保存记录失败:', err.message);
                  reject(err);
                  return;
                }
                resolve(this.lastID);
              }
            );
          }
        }
      );
    });
  });
}

// 更新扫描结果（带ID）
function updateScanResult(result) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const {
        id, host, port, status, connect_time, status_info, key_values,
        is_ransomware, auto_detected, is_synced, remote_id, is_uploaded, uploaded_at
      } = result;

      if (!id) {
        db.close();
        reject(new Error('更新记录需要提供ID'));
        return;
      }

      db.run(
        `UPDATE scan_results SET
        host = ?,
        port = ?,
        status = ?,
        connect_time = ?,
        status_info = ?,
        key_values = ?,
        is_ransomware = ?,
        auto_detected = ?,
        is_synced = ?,
        remote_id = ?,
        is_uploaded = ?,
        uploaded_at = ?
        WHERE id = ?`,
        [
          host,
          port,
          status,
          connect_time,
          status_info,
          key_values,
          is_ransomware || 0,
          auto_detected || 0,
          is_synced || 0,
          remote_id || null,
          is_uploaded || 0,
          uploaded_at || null,
          id
        ],
        function(err) {
          db.close();
          if (err) {
            console.error('更新记录失败:', err.message);
            reject(err);
            return;
          }
          resolve(this.changes);
        }
      );
    });
  });
}

// 保存测绘结果
function saveMappingResult(result) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const {
        ip, port, hostname, transport, asn, org, service_name,
        country_cn, province_cn, city_cn, service_response, service_cert,
        time, mapping_type, query_used, raw_data
      } = result;

      // 使用REPLACE INTO实现去重插入
      db.run(
        `REPLACE INTO mapping_results
        (ip, port, hostname, transport, asn, org, service_name, country_cn, province_cn, city_cn,
         service_response, service_cert, time, mapping_type, query_used, raw_data)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          ip, port, hostname, transport, asn, org, service_name,
          country_cn, province_cn, city_cn, service_response, service_cert,
          time, mapping_type, query_used, JSON.stringify(raw_data)
        ],
        function(err) {
          db.close();
          if (err) {
            console.error('保存测绘结果失败:', err.message);
            reject(err);
            return;
          }
          resolve(this.lastID);
        }
      );
    });
  });
}

// 批量保存测绘结果
function saveBatchMappingResults(results) {
  return new Promise((resolve, reject) => {
    if (!results || results.length === 0) {
      resolve(0);
      return;
    }

    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare(`REPLACE INTO mapping_results
          (ip, port, hostname, transport, asn, org, service_name, country_cn, province_cn, city_cn,
           service_response, service_cert, time, mapping_type, query_used, raw_data)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);

        let savedCount = 0;

        for (const result of results) {
          const {
            ip, port, hostname, transport, asn, org, service_name,
            country_cn, province_cn, city_cn, service_response, service_cert,
            time, mapping_type, query_used, raw_data
          } = result;

          stmt.run([
            ip, port, hostname, transport, asn, org, service_name,
            country_cn, province_cn, city_cn, service_response, service_cert,
            time, mapping_type, query_used, JSON.stringify(raw_data)
          ]);
          savedCount++;
        }

        stmt.finalize();

        db.run('COMMIT', (err) => {
          db.close();

          if (err) {
            console.error('批量保存测绘结果失败:', err.message);
            reject(err);
            return;
          }

          resolve(savedCount);
        });
      });
    });
  });
}

// 获取测绘结果
function getMappingResults(mappingType, limit = 1000) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = 'SELECT * FROM mapping_results';
      let params = [];

      if (mappingType) {
        query += ' WHERE mapping_type = ?';
        params.push(mappingType);
      }

      query += ' ORDER BY created_at DESC';

      if (limit > 0) {
        query += ' LIMIT ?';
        params.push(limit);
      }

      db.all(query, params, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取测绘结果失败:', err.message);
          reject(err);
          return;
        }

        // 解析JSON字符串
        const results = rows.map(row => {
          try {
            row.raw_data = JSON.parse(row.raw_data || '{}');
          } catch (e) {
            row.raw_data = {};
          }
          return row;
        });

        resolve(results);
      });
    });
  });
}

// 保存测绘任务
function saveMappingTask(task) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const {
        task_name, mapping_type, query_statement, total_count,
        fetched_count, status, pagination_id, start_time, end_time, error_message
      } = task;

      db.run(
        `INSERT INTO mapping_tasks
        (task_name, mapping_type, query_statement, total_count, fetched_count, status,
         pagination_id, start_time, end_time, error_message)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          task_name, mapping_type, query_statement, total_count || 0,
          fetched_count || 0, status || 'pending', pagination_id,
          start_time, end_time, error_message
        ],
        function(err) {
          db.close();
          if (err) {
            console.error('保存测绘任务失败:', err.message);
            reject(err);
            return;
          }
          resolve(this.lastID);
        }
      );
    });
  });
}

// 更新测绘任务
function updateMappingTask(taskId, updates) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const fields = [];
      const values = [];

      for (const [key, value] of Object.entries(updates)) {
        fields.push(`${key} = ?`);
        values.push(value);
      }

      if (fields.length === 0) {
        db.close();
        resolve(0);
        return;
      }

      // 添加updated_at字段
      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(taskId);

      const query = `UPDATE mapping_tasks SET ${fields.join(', ')} WHERE id = ?`;

      db.run(query, values, function(err) {
        db.close();
        if (err) {
          console.error('更新测绘任务失败:', err.message);
          reject(err);
          return;
        }
        resolve(this.changes);
      });
    });
  });
}

// 获取测绘任务
function getMappingTasks(status) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = 'SELECT * FROM mapping_tasks';
      let params = [];

      if (status) {
        query += ' WHERE status = ?';
        params.push(status);
      }

      query += ' ORDER BY created_at DESC';

      db.all(query, params, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取测绘任务失败:', err.message);
          reject(err);
          return;
        }

        resolve(rows);
      });
    });
  });
}

// 清空测绘结果
function clearMappingResults(mappingType) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = 'DELETE FROM mapping_results';
      let params = [];

      if (mappingType) {
        query += ' WHERE mapping_type = ?';
        params.push(mappingType);
      }

      db.run(query, params, function(err) {
        db.close();
        if (err) {
          console.error('清空测绘结果失败:', err.message);
          reject(err);
          return;
        }
        resolve(this.changes);
      });
    });
  });
}

// 标记测绘结果为已测试
function markMappingResultAsTested(ip, port) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const testedTime = formatDateForMySQL(new Date());

      db.run(
        `UPDATE mapping_results SET is_tested = 1, tested_time = ? WHERE ip = ? AND port = ?`,
        [testedTime, ip, port],
        function(err) {
          db.close();
          if (err) {
            console.error('标记测绘结果为已测试失败:', err.message);
            reject(err);
            return;
          }
          resolve(this.changes);
        }
      );
    });
  });
}

// 批量标记测绘结果为已测试
function markMappingResultsAsTested(targets) {
  return new Promise((resolve, reject) => {
    if (!targets || targets.length === 0) {
      resolve(0);
      return;
    }

    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const testedTime = formatDateForMySQL(new Date());

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare(`UPDATE mapping_results SET is_tested = 1, tested_time = ? WHERE ip = ? AND port = ?`);

        let updatedCount = 0;

        for (const target of targets) {
          const [ip, port] = target.split(':');
          if (ip && port) {
            stmt.run([testedTime, ip, parseInt(port)]);
            updatedCount++;
          }
        }

        stmt.finalize();

        db.run('COMMIT', (err) => {
          db.close();

          if (err) {
            console.error('批量标记测绘结果为已测试失败:', err.message);
            reject(err);
            return;
          }

          resolve(updatedCount);
        });
      });
    });
  });
}

// 获取未测试的测绘结果
function getUntestedMappingResults(mappingType, limit = 1000) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = 'SELECT * FROM mapping_results WHERE is_tested = 0';
      let params = [];

      if (mappingType) {
        query += ' AND mapping_type = ?';
        params.push(mappingType);
      }

      query += ' ORDER BY created_at DESC';

      if (limit > 0) {
        query += ' LIMIT ?';
        params.push(limit);
      }

      db.all(query, params, (err, rows) => {
        db.close();

        if (err) {
          console.error('获取未测试测绘结果失败:', err.message);
          reject(err);
          return;
        }

        // 解析JSON字符串
        const results = rows.map(row => {
          try {
            row.raw_data = JSON.parse(row.raw_data || '{}');
          } catch (e) {
            row.raw_data = {};
          }
          return row;
        });

        resolve(results);
      });
    });
  });
}

// 获取测绘统计信息
function getMappingStats(mappingType) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      let query = `
        SELECT
          COUNT(*) as total_count,
          COUNT(CASE WHEN is_tested = 1 THEN 1 END) as tested_count,
          COUNT(CASE WHEN is_tested = 0 THEN 1 END) as untested_count,
          MAX(time) as latest_time,
          MIN(time) as earliest_time
        FROM mapping_results
      `;
      let params = [];

      if (mappingType) {
        query += ' WHERE mapping_type = ?';
        params.push(mappingType);
      }

      db.get(query, params, (err, row) => {
        db.close();

        if (err) {
          console.error('获取测绘统计信息失败:', err.message);
          reject(err);
          return;
        }

        resolve(row || {
          total_count: 0,
          tested_count: 0,
          untested_count: 0,
          latest_time: null,
          earliest_time: null
        });
      });
    });
  });
}

module.exports = {
  initDb,
  saveScanResult,
  saveScanResultWithSync,
  updateScanResult,
  saveBatchScanResults,
  getScanResults,
  getUnuploadedResults,
  markResultsAsUploaded,
  updateRansomwareStatus,
  saveRansomwareFeature,
  updateRansomwareFeature,
  getRansomwareFeatures,
  deleteRansomwareFeature,
  getConfig,
  updateConfig,
  clearScanResults,
  deduplicateScanResults,
  syncFeaturesFromRemote,
  syncScanResultsFromRemote,
  clearRansomwareFeatures,
  markFeatureAsSynced,
  getUnsyncedFeatures,
  // 测绘相关函数
  saveMappingResult,
  saveBatchMappingResults,
  getMappingResults,
  saveMappingTask,
  updateMappingTask,
  getMappingTasks,
  clearMappingResults,
  markMappingResultAsTested,
  markMappingResultsAsTested,
  getUntestedMappingResults,
  getMappingStats,
  // 仪表盘相关函数
  getDashboardStats,
  getProvinceStats,
  getOrgStats,
  getPortStats,
  getTimeTrendStats
};

// 获取仪表盘统计数据
async function getDashboardStats() {
  try {
    const scanResults = await getScanResults();
    const mappingResults = await getMappingResults();

    // 扫描结果统计
    const totalScans = scanResults.length;
    const successScans = scanResults.filter(r => r.status === 1).length;
    const failScans = scanResults.filter(r => r.status === 0).length;
    const ransomwareScans = scanResults.filter(r => r.is_ransomware === 1).length;

    // 测绘结果统计
    const totalMapping = mappingResults.length;
    const testedMapping = mappingResults.filter(r => r.is_tested === 1).length;
    const untestedMapping = mappingResults.filter(r => r.is_tested === 0).length;

    return {
      scan: {
        total: totalScans,
        success: successScans,
        fail: failScans,
        ransomware: ransomwareScans,
        successRate: totalScans > 0 ? ((successScans / totalScans) * 100).toFixed(1) : 0,
        ransomwareRate: successScans > 0 ? ((ransomwareScans / successScans) * 100).toFixed(1) : 0
      },
      mapping: {
        total: totalMapping,
        tested: testedMapping,
        untested: untestedMapping,
        testedRate: totalMapping > 0 ? ((testedMapping / totalMapping) * 100).toFixed(1) : 0
      }
    };
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error.message);
    return {
      scan: { total: 0, success: 0, fail: 0, ransomware: 0, successRate: 0, ransomwareRate: 0 },
      mapping: { total: 0, tested: 0, untested: 0, testedRate: 0 }
    };
  }
}

// 获取省份统计数据
async function getProvinceStats(dataSource = 'mapping') {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const query = dataSource === 'mapping'
        ? 'SELECT province_cn, COUNT(*) as total, SUM(CASE WHEN is_tested = 1 THEN 1 ELSE 0 END) as tested FROM mapping_results WHERE province_cn IS NOT NULL AND province_cn != "" GROUP BY province_cn ORDER BY total DESC'
        : `SELECT
             COALESCE(m.province_cn, '未知') as province_cn,
             COUNT(s.id) as total,
             SUM(CASE WHEN s.status = 1 THEN 1 ELSE 0 END) as success,
             SUM(CASE WHEN s.is_ransomware = 1 THEN 1 ELSE 0 END) as ransomware
           FROM scan_results s
           LEFT JOIN mapping_results m ON s.host = m.ip AND s.port = m.port
           GROUP BY m.province_cn
           ORDER BY total DESC`;

      db.all(query, [], (err, rows) => {
        db.close();
        if (err) {
          console.error('获取省份统计数据失败:', err.message);
          reject(err);
          return;
        }

        const results = rows.map(row => ({
          province: row.province_cn || '未知',
          total: row.total || 0,
          success: row.success || row.tested || 0,
          ransomware: row.ransomware || 0,
          successRate: row.total > 0 ? ((row.success || row.tested || 0) / row.total * 100).toFixed(1) : 0,
          ransomwareRate: (row.success || row.tested || 0) > 0 ? ((row.ransomware || 0) / (row.success || row.tested || 0) * 100).toFixed(1) : 0
        }));

        resolve(results);
      });
    });
  });
}

// 获取组织统计数据
async function getOrgStats(dataSource = 'mapping') {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const query = dataSource === 'mapping'
        ? 'SELECT org, COUNT(*) as total, SUM(CASE WHEN is_tested = 1 THEN 1 ELSE 0 END) as tested FROM mapping_results WHERE org IS NOT NULL AND org != "" GROUP BY org ORDER BY total DESC LIMIT 20'
        : `SELECT
             COALESCE(m.org, '未知') as org,
             COUNT(s.id) as total,
             SUM(CASE WHEN s.status = 1 THEN 1 ELSE 0 END) as success,
             SUM(CASE WHEN s.is_ransomware = 1 THEN 1 ELSE 0 END) as ransomware
           FROM scan_results s
           LEFT JOIN mapping_results m ON s.host = m.ip AND s.port = m.port
           GROUP BY m.org
           ORDER BY total DESC
           LIMIT 20`;

      db.all(query, [], (err, rows) => {
        db.close();
        if (err) {
          console.error('获取组织统计数据失败:', err.message);
          reject(err);
          return;
        }

        const results = rows.map(row => ({
          org: row.org || '未知',
          total: row.total || 0,
          success: row.success || row.tested || 0,
          ransomware: row.ransomware || 0,
          successRate: row.total > 0 ? ((row.success || row.tested || 0) / row.total * 100).toFixed(1) : 0,
          ransomwareRate: (row.success || row.tested || 0) > 0 ? ((row.ransomware || 0) / (row.success || row.tested || 0) * 100).toFixed(1) : 0
        }));

        resolve(results);
      });
    });
  });
}

// 获取端口统计数据
async function getPortStats(limit = 10) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const query = `
        SELECT
          port,
          COUNT(*) as total,
          SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
          SUM(CASE WHEN is_ransomware = 1 THEN 1 ELSE 0 END) as ransomware
        FROM scan_results
        GROUP BY port
        ORDER BY total DESC
        LIMIT ?
      `;

      db.all(query, [limit], (err, rows) => {
        db.close();
        if (err) {
          console.error('获取端口统计数据失败:', err.message);
          reject(err);
          return;
        }

        const results = rows.map(row => ({
          port: row.port,
          total: row.total,
          success: row.success,
          ransomware: row.ransomware,
          successRate: row.total > 0 ? (row.success / row.total * 100).toFixed(1) : 0,
          ransomwareRate: row.success > 0 ? (row.ransomware / row.success * 100).toFixed(1) : 0
        }));

        resolve(results);
      });
    });
  });
}

// 获取时间趋势数据
async function getTimeTrendStats(days = 7) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(scanDbPath, (err) => {
      if (err) {
        console.error('扫描结果数据库连接失败:', err.message);
        reject(err);
        return;
      }

      const query = `
        SELECT
          DATE(connect_time) as date,
          COUNT(*) as total,
          SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success,
          SUM(CASE WHEN is_ransomware = 1 THEN 1 ELSE 0 END) as ransomware
        FROM scan_results
        WHERE connect_time >= datetime('now', '-${days} days')
        GROUP BY DATE(connect_time)
        ORDER BY date ASC
      `;

      db.all(query, [], (err, rows) => {
        db.close();
        if (err) {
          console.error('获取时间趋势数据失败:', err.message);
          reject(err);
          return;
        }

        const results = rows.map(row => ({
          date: row.date,
          total: row.total,
          success: row.success,
          ransomware: row.ransomware
        }));

        resolve(results);
      });
    });
  });
}