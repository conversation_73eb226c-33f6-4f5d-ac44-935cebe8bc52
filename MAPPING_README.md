# Redis测绘模块使用指南

## 概述

Redis测绘模块集成了Quake测绘引擎，可以自动获取境内Redis服务目标，为扫描模块提供数据源。支持两种预定义的测绘模式，无需手动编写查询语句。

## 功能特性

### 1. 预定义测绘模式
- **境内Redis未授权**: 检测境内所有Redis未授权访问服务
- **境内Redis挖矿**: 检测境内可能被用于挖矿的Redis服务

### 2. 智能数据管理
- 自动去重存储测绘结果
- 本地数据库持久化保存
- 支持大批量数据获取（最多10000条）
- 实时进度显示和统计

### 3. 无缝集成扫描
- 一键添加测绘目标到扫描列表
- 自动切换到批量扫描模式
- 支持导出目标列表为文本文件

## 使用方法

### 1. 配置Quake API Token

#### 获取Token
1. 访问 [Quake官网](https://quake.360.net)
2. 注册并登录账户
3. 在个人中心获取API Token

#### 配置Token
**方法一：在测绘页面配置**
1. 切换到"测绘"标签页
2. 在"Quake API Token"输入框中输入Token
3. 点击"验证Token"按钮验证

**方法二：在设置页面配置**
1. 切换到"设置"标签页
2. 在"测绘引擎配置"部分输入Token
3. 点击"保存设置"

### 2. 执行测绘任务

#### 基本步骤
1. 确保已配置并验证Token
2. 选择测绘模式：
   - **境内Redis未授权**: 获取所有未授权Redis服务
   - **境内Redis挖矿**: 获取疑似挖矿Redis服务
3. 设置参数：
   - **最大结果数**: 建议1000-5000（避免消耗过多积分）
   - **批次大小**: 建议100-200（单次请求数据量）
4. 点击"开始测绘"

#### 实时监控
- 查看用户信息和剩余积分
- 观察实时进度条和数据获取状态
- 监控测绘结果统计信息

### 3. 管理测绘结果

#### 查看结果
- 测绘结果实时显示在表格中
- 包含IP地址、端口、地理位置、组织信息等
- 支持分页浏览（每页50条）

#### 导出目标
1. 点击"导出目标"按钮
2. 选择保存位置
3. 生成格式为 `IP:端口` 的文本文件

#### 添加到扫描
1. 点击结果表格中的"添加到扫描"按钮
2. 自动切换到扫描页面
3. 目标自动添加到批量扫描列表

#### 清空结果
- 点击"清空结果"按钮
- 确认后清空所有本地测绘数据

## 测绘模式详解

### 境内Redis未授权
```
查询语句: protocol:"redis" AND response:"redis_version" AND country:"中国" AND (NOT province:"台湾" AND NOT province:"香港" AND NOT province:"澳门")
```
- **目标**: 检测境内所有Redis服务
- **特点**: 覆盖面广，包含所有可访问的Redis实例
- **用途**: 全面的Redis服务发现和安全评估

### 境内Redis挖矿
```
查询语句: protocol:"redis" AND response:"db0:keys=4," AND response:"redis_version" AND country:"中国" AND (NOT province:"台湾" AND NOT province:"香港" AND NOT province:"澳门")
```
- **目标**: 检测疑似被用于挖矿的Redis服务
- **特点**: 通过特定响应特征筛选
- **用途**: 针对性的挖矿威胁检测

## 技术实现

### 架构设计
```
测绘模块 (Mapping Module)
├── Quake API 客户端
├── 数据解析器
├── 本地存储管理
└── UI交互控制

数据流程:
Quake API → 数据解析 → 去重处理 → 本地存储 → UI显示
```

### 核心文件
- `src/quake-mapper.js` - Quake API客户端和测绘逻辑
- `src/database.js` - 测绘数据存储和管理
- `src/renderer.js` - 测绘UI交互和控制
- `src/index.html` - 测绘页面界面

### 数据库表结构
- `mapping_results` - 测绘结果存储
- `mapping_tasks` - 测绘任务记录

## 性能优化

### 批量处理
- 支持分批获取数据，避免单次请求过大
- 自动处理分页，无需手动翻页
- 实时保存数据，防止数据丢失

### 去重机制
- 基于IP+端口+测绘类型的唯一性约束
- 自动更新重复数据，保持最新状态
- 减少存储空间占用

### 错误处理
- 网络异常自动重试
- API限制友好提示
- 任务中断恢复机制

## 注意事项

### 积分消耗
- 每次API调用都会消耗积分
- 建议合理设置最大结果数
- 定期检查剩余积分

### 网络要求
- 需要稳定的互联网连接
- 确保能访问Quake API服务
- 建议在网络良好时执行大批量测绘

### 数据合规
- 仅用于安全研究和授权测试
- 遵守相关法律法规
- 不得用于非法用途

## 故障排除

### 常见问题

1. **Token验证失败**
   - 检查Token是否正确
   - 确认账户状态正常
   - 验证网络连接

2. **测绘无结果**
   - 检查查询条件是否过于严格
   - 确认目标地区是否有相关服务
   - 尝试调整批次大小

3. **积分不足**
   - 登录Quake官网查看积分余额
   - 考虑购买更多积分
   - 减少单次查询的结果数量

### 调试方法
- 查看浏览器开发者工具控制台
- 检查网络请求状态
- 验证API响应内容

## 最佳实践

1. **合理规划**: 根据需求选择合适的测绘模式
2. **分批执行**: 大量数据分多次获取，避免超时
3. **定期清理**: 及时清理过期的测绘数据
4. **积分管理**: 监控积分使用，合理分配查询额度
5. **数据备份**: 重要测绘结果及时导出备份

---

**提示**: 测绘功能需要有效的Quake API Token，请确保账户有足够的积分进行查询。合理使用测绘功能，可以大大提高Redis安全检测的效率和覆盖面。
